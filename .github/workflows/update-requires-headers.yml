name: Update Requires Headers

on:
  schedule:
    - cron: '0 0 * * *' # Run every day at midnight UTC
  workflow_dispatch: # Allow manual trigger

env:
  PLUGIN_FILE_NAME: "woocommerce-shipstation.php"
  MAIN_BRANCH: "trunk"

jobs:
  update-headers:
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout the repository
      - name: Checkout Repository
        uses: actions/checkout@v3

      # Step 2: Fetch the latest WooCommerce release
      - name: Fetch Latest WooCommerce Release
        id: fetch_wc_release
        run: |
          # Download the latest WooCommerce release
          echo "Fetching the latest stable release..."
          LATEST_RELEASE=$(curl -s https://api.github.com/repos/woocommerce/woocommerce/releases/latest | jq -r '.tag_name')
          echo "Stable release: $LATEST_RELEASE"
          
          echo "Fetching the latest RC release..."
          RC_RELEASE=$(curl -s https://api.github.com/repos/woocommerce/woocommerce/releases | jq -r '[.[] | select(.tag_name | ascii_downcase | contains("rc"))] | sort_by(.published_at) | reverse | .[0].tag_name')
          echo "RC release: $RC_RELEASE"
          
          # Default to stable release
          DOWNLOAD_TAG="$LATEST_RELEASE"
          
          if [ -n "$RC_RELEASE" ]; then
          # Remove any leading "v" to compare version numbers correctly
          STABLE_VERSION=$(echo "$LATEST_RELEASE" | sed 's/^v//')
          RC_VERSION=$(echo "$RC_RELEASE" | sed 's/^v//')
          
          # Compare versions using sort -V. The highest version will be last.
          HIGHEST=$(echo -e "$RC_VERSION\n$STABLE_VERSION" | sort -V | tail -n 1)
          if [ "$HIGHEST" = "$RC_VERSION" ] && [ "$RC_VERSION" != "$STABLE_VERSION" ]; then
          echo "RC release is newer than stable release."
          DOWNLOAD_TAG="$RC_RELEASE"
          else
          echo "Stable release is newer or equal to RC release."
          fi
          else
          echo "No RC release found."
          fi
          
          echo "Downloading release: $DOWNLOAD_TAG"
          curl -L -o woocommerce.zip "https://github.com/woocommerce/woocommerce/releases/download/$DOWNLOAD_TAG/woocommerce.zip"
          
          # Unzip WooCommerce release
          unzip -q woocommerce.zip
          
          # Extract minor version for WC requires at least
          LATEST_RELEASE=$(echo "$DOWNLOAD_TAG" | sed -E 's/^v?([0-9]+\.[0-9]+\.[0-9]+).*/\1/')
          MAJOR_VERSION=$(echo "$LATEST_RELEASE" | grep -oE '^[0-9]+')
          MINOR_VERSION=$(echo "$LATEST_RELEASE" | grep -oE '^[0-9]+\.[0-9]+' | cut -d. -f2)
          ADJUSTED_MINOR_VERSION=$((MINOR_VERSION - 2))

          if [ "$MINOR_VERSION" -le 0 ]; then
            ADJUSTED_MAJOR_VERSION=$((MAJOR_VERSION - 1))
            ADJUSTED_MINOR_VERSION=8
          else
            ADJUSTED_MAJOR_VERSION=$MAJOR_VERSION
            ADJUSTED_MINOR_VERSION=$((MINOR_VERSION - 2))
          fi
          
          LATEST_MINOR_RELEASE="$ADJUSTED_MAJOR_VERSION.$ADJUSTED_MINOR_VERSION"
          
          echo "Latest WooCommerce Release: $LATEST_RELEASE"
          echo "LATEST_RELEASE=$LATEST_RELEASE" >> $GITHUB_ENV
          echo "LATEST_MINOR_RELEASE=$LATEST_MINOR_RELEASE" >> $GITHUB_ENV

      # Step 3: Create and Checkout a New Branch
      - name: Create and Checkout Branch
        run: |
          BRANCH_NAME="tweak/update-requires-headers-${{ env.LATEST_RELEASE }}"
          git checkout -b "$BRANCH_NAME"
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV

      # Step 4: Compare and Update Headers
      - name: Compare and Update Headers
        id: compare_and_update
        run: |
          WC_CORE_FILE="woocommerce/woocommerce.php"
          EXTENSION_FILE=${{ env.PLUGIN_FILE_NAME }}
          CHANGES_MADE=false

          # Get required versions from WooCommerce core
          REQUIRES_AT_LEAST=$(grep -oP 'Requires at least: \K[\d.]+' "$WC_CORE_FILE")
          REQUIRES_PHP=$(grep -oP 'Requires PHP: \K[\d.]+' "$WC_CORE_FILE")
          LATEST_MINOR_RELEASE=${{ env.LATEST_MINOR_RELEASE }}
          echo "REQUIRES_AT_LEAST=$REQUIRES_AT_LEAST" >> $GITHUB_ENV
          echo "REQUIRES_PHP=$REQUIRES_PHP" >> $GITHUB_ENV

          # -------------------------------
          # Compare and update 'Requires at least'
          # -------------------------------
          if grep -qE "^\s*\* Requires at least:" "$EXTENSION_FILE"; then
            current_req=$(grep -oP '^\s*\* Requires at least:\s*\K[\d.]+' "$EXTENSION_FILE")
            echo "Current 'Requires at least' header: $current_req"
            if [ "$(printf "%s\n%s" "$current_req" "$REQUIRES_AT_LEAST" | sort -V | head -n1)" = "$current_req" ] && [ "$current_req" != "$REQUIRES_AT_LEAST" ]; then
              echo "Updating 'Requires at least' header from $current_req to $REQUIRES_AT_LEAST"
              sed -i "s#^\s*\* Requires at least:.*# * Requires at least: $REQUIRES_AT_LEAST#" "$EXTENSION_FILE"
              CHANGES_MADE=true
            else
              echo "Current 'Requires at least' ($current_req) is greater than or equal to required ($REQUIRES_AT_LEAST); not updating."
            fi
          else
            echo "Adding missing 'Requires at least' header."
            sed -i "/^\s*\* Version:/a \\ \\* Requires at least: $REQUIRES_AT_LEAST" "$EXTENSION_FILE"
            CHANGES_MADE=true
          fi

          # -------------------------------
          # Compare and update 'Requires PHP'
          # -------------------------------
          if grep -qE "^\s*\* Requires PHP:" "$EXTENSION_FILE"; then
            current_php=$(grep -oP '^\s*\* Requires PHP:\s*\K[\d.]+' "$EXTENSION_FILE")
            echo "Current 'Requires PHP' header: $current_php"
            if [ "$(printf "%s\n%s" "$current_php" "$REQUIRES_PHP" | sort -V | head -n1)" = "$current_php" ] && [ "$current_php" != "$REQUIRES_PHP" ]; then
              echo "Updating 'Requires PHP' header from $current_php to $REQUIRES_PHP"
              sed -i "s#^\s*\* Requires PHP:.*# * Requires PHP: $REQUIRES_PHP#" "$EXTENSION_FILE"
              CHANGES_MADE=true
            else
              echo "Current 'Requires PHP' ($current_php) is greater than or equal to required ($REQUIRES_PHP); not updating."
            fi
          else
            echo "Adding missing 'Requires PHP' header."
            sed -i "/^\s*\* Version:/a \\ \\* Requires PHP: $REQUIRES_PHP" "$EXTENSION_FILE"
            CHANGES_MADE=true
          fi

          # -------------------------------
          # Compare and update 'WC requires at least'
          # -------------------------------
          if grep -qE "^\s*\* WC requires at least:" "$EXTENSION_FILE"; then
            current_wc=$(grep -oP '^\s*\* WC requires at least:\s*\K[\d.]+' "$EXTENSION_FILE")
            echo "Current 'WC requires at least' header: $current_wc"
            if [ "$(printf "%s\n%s" "$current_wc" "$LATEST_MINOR_RELEASE" | sort -V | head -n1)" = "$current_wc" ] && [ "$current_wc" != "$LATEST_MINOR_RELEASE" ]; then
              echo "Updating 'WC requires at least' header from $current_wc to $LATEST_MINOR_RELEASE"
              sed -i "s#^\s*\* WC requires at least:.*# * WC requires at least: $LATEST_MINOR_RELEASE#" "$EXTENSION_FILE"
              CHANGES_MADE=true
            else
              echo "Current 'WC requires at least' ($current_wc) is greater than or equal to required ($LATEST_MINOR_RELEASE); not updating."
            fi
          else
            echo "Adding missing 'WC requires at least' header."
            sed -i "/^\s*\* Version:/a \\ \\* WC requires at least: $LATEST_MINOR_RELEASE" "$EXTENSION_FILE"
            CHANGES_MADE=true
          fi

          echo "EDITED_FILES=${{ env.PLUGIN_FILE_NAME }}" >> $GITHUB_ENV
          echo "CHANGES_MADE=$CHANGES_MADE" >> $GITHUB_ENV

      - name: Compare and Update Headers in README File
        run: |
          README_FILE="readme.txt"
          CHANGES_MADE=${{ env.CHANGES_MADE }}

          if [ -f "$README_FILE" ]; then
            echo "Found $README_FILE. Updating headers..."

            # -------------------------------
            # Compare and update 'Requires at least'
            # -------------------------------
            if grep -qE "^Requires at least:" "$README_FILE"; then
              current_req=$(grep -oP '^Requires at least:\s*\K[\d.]+' "$README_FILE")
              echo "Current 'Requires at least' in README: $current_req"
              if [ "$(printf "%s\n%s" "$current_req" "$REQUIRES_AT_LEAST" | sort -V | head -n1)" = "$current_req" ] && [ "$current_req" != "$REQUIRES_AT_LEAST" ]; then
                echo "Updating 'Requires at least' header to $REQUIRES_AT_LEAST"
                sed -i "s#^Requires at least:.*#Requires at least: $REQUIRES_AT_LEAST#" "$README_FILE"
                CHANGES_MADE=true
              else
                echo "Existing 'Requires at least' ($current_req) is greater than or equal to required ($REQUIRES_AT_LEAST); not updating."
              fi
            else
              echo "Adding missing 'Requires at least' header."
              sed -i "/^Stable tag:/i Requires at least: $REQUIRES_AT_LEAST" "$README_FILE"
              CHANGES_MADE=true
            fi

            # -------------------------------
            # Compare and update 'Requires PHP'
            # -------------------------------
            if grep -qE "^Requires PHP:" "$README_FILE"; then
              current_php=$(grep -oP '^Requires PHP:\s*\K[\d.]+' "$README_FILE")
              echo "Current 'Requires PHP' in README: $current_php"
              if [ "$(printf "%s\n%s" "$current_php" "$REQUIRES_PHP" | sort -V | head -n1)" = "$current_php" ] && [ "$current_php" != "$REQUIRES_PHP" ]; then
                echo "Updating 'Requires PHP' header to $REQUIRES_PHP"
                sed -i "s#^Requires PHP:.*#Requires PHP: $REQUIRES_PHP#" "$README_FILE"
                CHANGES_MADE=true
              else
                echo "Existing 'Requires PHP' ($current_php) is greater than or equal to required ($REQUIRES_PHP); not updating."
              fi
            else
              echo "Adding missing 'Requires PHP' header."
              sed -i "/^Stable tag:/i Requires PHP: $REQUIRES_PHP" "$README_FILE"
              CHANGES_MADE=true
            fi

            # -------------------------------
            # Compare and update 'WC requires at least'
            # -------------------------------
            if grep -qE "^WC requires at least:" "$README_FILE"; then
              current_wc=$(grep -oP '^WC requires at least:\s*\K[\d.]+' "$README_FILE")
              echo "Current 'WC requires at least' in README: $current_wc"
              if [ "$(printf "%s\n%s" "$current_wc" "$LATEST_MINOR_RELEASE" | sort -V | head -n1)" = "$current_wc" ] && [ "$current_wc" != "$LATEST_MINOR_RELEASE" ]; then
                echo "Updating 'WC requires at least' header to $LATEST_MINOR_RELEASE"
                sed -i "s#^WC requires at least:.*#WC requires at least: $LATEST_MINOR_RELEASE#" "$README_FILE"
                CHANGES_MADE=true
              else
                echo "Existing 'WC requires at least' ($current_wc) is greater than or equal to required ($LATEST_MINOR_RELEASE); not updating."
              fi
            else
              echo "Adding missing 'WC requires at least' header."
              sed -i "/^Stable tag:/i WC requires at least: $LATEST_MINOR_RELEASE" "$README_FILE"
              CHANGES_MADE=true
            fi

            echo "CHANGES_MADE=$CHANGES_MADE" >> $GITHUB_ENV
            echo "FILES_TO_CHECK=${{ env.PLUGIN_FILE_NAME }},readme.txt" >> $GITHUB_ENV
          else
            echo "$README_FILE not found."
          fi

      # Step 5: Ignore Unwanted Files
      - name: Ignore Unwanted Files
        run: |
          echo "woocommerce/" >> .gitignore
          echo "woocommerce.zip" >> .gitignore
          git clean -fdx

      # Step 5: Push Branch
      - name: Push Branch
        if: env.CHANGES_MADE == 'true'
        run: |
          git push --set-upstream origin ${{ env.BRANCH_NAME }}

      # Step 6: Create Pull Request
      - name: Create Pull Request
        if: env.CHANGES_MADE == 'true'
        uses: peter-evans/create-pull-request@v7
        with:
          token: ${{ secrets.WCSHIPBOT_GITHUB_TOKEN }}
          branch: ${{ env.BRANCH_NAME }}
          base: ${{ env.MAIN_BRANCH }}
          add-paths: ${{ env.EDITED_FILES }}
          commit-message: "Update Requires headers"
          title: "Update Requires headers for WooCommerce compatibility"
          reviewers: |
            dustinparker
            iyut
            bartech
            abdalsalaam
          body: |
            This PR updates plugin headers to ensure compatibility with the latest WooCommerce release.

            Please test the plugin and merge.