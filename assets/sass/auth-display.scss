// ===== ShipStation Auth Modal (WooCommerce-style) =====
$ss-surface: #fff;
$ss-text: #1e1e1e;
$ss-muted: #757575;
$ss-border: #e0e0e0;
$ss-backdrop: rgba(17, 24, 39, .45);
$ss-focus: #007cba;
$ss-success: #46b450;
$ss-shadow-lg: 0 20px 40px rgba(0,0,0,.18), 0 2px 6px rgba(0,0,0,.08);
$ss-shadow-sm: 0 6px 16px rgba(0,0,0,.12), 0 1px 2px rgba(0,0,0,.06);
$ss-gap: 16px;
$ss-gap-lg: 24px;
$ss-line: 1.6;

@mixin button-reset {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid transparent;
  background: transparent;
  padding: 0;
}
@mixin ss-overlay($bg, $padding: 0) {
  position: absolute;
  inset: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  background: $bg;
  padding: $padding;
}

.shipstation-modal {
  position: fixed;
  inset: 0;
  z-index: 100000;
  display: none;

  &.is-open { display: block; }

  &-backdrop {
    position: absolute;
    inset: 0;
    background: $ss-backdrop;
    transition: opacity .2s ease-in-out;
  }

  &-content {
    position: relative;
    border-radius: 8px;
    box-sizing: border-box;
    margin: 6vh auto;
    max-width: 720px;
    width: min(96vw, 720px);
    background: $ss-surface;
    color: $ss-text;
    box-shadow: $ss-shadow-lg;
    overflow: hidden;
    transform: translateY(10px);
    transition: transform .2s ease, opacity .2s ease;
  }

  &.is-open &-content {
    transform: translateY(0);
  }

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $ss-gap-lg;
    border-bottom: 1px solid $ss-border;
    background: #fafafa;

    h2 {
      margin: 0;
      font-size: 18px;
      line-height: 1.4;
      font-weight: 600;
    }

    .shipstation-modal-close {
      @include button-reset;
      width: 40px;
      height: 40px;
      transition: background .15s ease, border-color .15s ease, transform .06s ease;

      &:hover {
        background: #f1f1f1;
        border-color: $ss-border;
      }
      &:active { transform: scale(.98); }

      .dashicons { font-size: 18px; line-height: 1; pointer-events: none; }
    }
  }

  &-body {
    padding: $ss-gap-lg;
    max-height: calc(100vh - 220px);
    overflow: auto;

    p {
      margin: 0 0 $ss-gap;
      color: $ss-muted;
      line-height: $ss-line;
    }

    .shipstation-auth-field {
      display: grid;
      grid-template-columns: 200px 1fr;
      gap: 10px $ss-gap;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px dashed $ss-border;

      &:last-child { border-bottom: 0; }

      label {
        font-weight: 600;
        color: #2c3338;
      }

      .shipstation-field-wrapper {
        display: flex;
        gap: 8px;
        align-items: center;

        input[type="text"],
        input[type="password"] {
          width: 100%;
          height: 40px;
          padding: 10px 12px;
          font-size: 14px;
          line-height: 1.4;
          border: 1px solid $ss-border;
          background: #fff;
          color: $ss-text;
          box-shadow: 0 1px 0 rgba(0,0,0,.02) inset;
          border-radius: 0;

          &[readonly] { background: #fbfbfb; }
          &:focus {
            border-color: $ss-focus;
            box-shadow: 0 0 0 1px $ss-focus;
            outline: none;
          }
        }

        .shipstation-toggle-visibility,
        .shipstation-copy-btn {
          @include button-reset;
          min-width: 40px;
          height: 40px;
          padding: 0 8px;
          border-color: $ss-border;
          background: #fff;
          transition: background .15s ease, border-color .15s ease, transform .06s ease, box-shadow .15s ease;

          &:hover {
            background: #f6f7f7;
            border-color: #c3c4c7;
          }
          .dashicons { font-size: 16px; line-height: 1; }
        }

        .shipstation-copy-btn.is-copied {
          border-color: $ss-success;
          box-shadow: 0 0 0 2px rgba(70,180,80,.2);

          &::after {
            font-size: 12px;
            margin-inline-start: 8px;
            color: darken($ss-success, 20%);
          }
        }
      }
    }

    #shipstation-first-view{
      .shipstation-auth-field:last-child {
        border-bottom: 1px dashed $ss-border;
      }
    }
  }

  &-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: $ss-gap-lg;
    border-top: 1px solid $ss-border;
    background: #fafafa;

    .button {
      min-height: 40px;
      border-radius: 0;

      .button-danger{
        color: #ffffff;
        background: #d63638;
        border-color: #d63638;

        &:hover {
          background: #b32d2e;
          border-color: #b32d2e;
        }
      }
    }
  }

  .shipstation-loading-overlay {
    @include ss-overlay(rgba(255, 255, 255, 0.95));
    .spinner {
      margin: 0;
      &.is-active { display: inline-block; }
    }
  }

  .shipstation-error-overlay {
    @include ss-overlay(rgba(255, 255, 255, 0.95), 20px);
    .shipstation-error-box {
      max-width: 560px;
      width: min(92vw, 560px);
      background: $ss-surface;
      border: 1px solid $ss-border;
      border-radius: 8px;
      box-shadow: $ss-shadow-sm;
      padding: $ss-gap-lg;

      h3 {
        margin: 0 0 8px;
        font-size: 16px;
        line-height: 1;
        font-weight: 600;
        color: #2c3338;
      }

      p {
        margin: 0;
        color: $ss-muted;
        line-height: $ss-line;
      }
    }
  }
}

body.rtl {
  .shipstation-copy-btn.is-copied::after {
    margin-inline-start: 0;
    margin-inline-end: 8px;
  }
  .shipstation-auth-field { direction: rtl; }
}
