.wc-shipstation-notification {
	padding:10px;
	border-bottom:2px solid #e0e0e0;
	margin-bottom:15px;

	.notification-info {
		margin-bottom:5px;
		display:flex;
		flex-direction:row;
		justify-content:flex-start;

		.notification-label {
			font-weight:700;
			display:block;
			width:150px;
		}

		.notification-value {
			&:before {
				content:": ";
			}
		}

		&.notification-items {
			display:block;

			.notification-item-row {
				margin-bottom:7px;

				.item-product {
					display:flex;
					flex-direction:row;
					justify-content:flex-start;
					align-items:center;

					&:before {
						content:"-";
					}

					.product-name {
						padding:5px;
						width:130px;

						span {
							display:block;

							&.description {
								line-height:1.5em;
							}

							&.sku {
								text-transform: lowercase;
								font-size:0.92em;
								color: #888;
							}
						}
					}

					.product-qty {
						padding:5px;
						font-weight:700;

						&:before {
							content:"x";
						}
					}
				}
			}
		}
	}
}