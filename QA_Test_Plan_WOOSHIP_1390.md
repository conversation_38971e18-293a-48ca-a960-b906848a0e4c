# QA Test Plan: ShipStation REST API Enhancement (WOOSHIP-1390)

## Overview
This test plan covers the new REST API endpoints for order export/import functionality introduced in the fix/issue-wooship-1390 branch.

## Test Environment Setup
- WordPress 6.7+
- WooCommerce 9.9+
- PHP 7.4+
- ShipStation plugin activated
- Test orders with various statuses
- API testing tool (Postman/Insomnia)

## 1. REST API Endpoints Testing

### 1.1 Orders Endpoint - GET /wp-json/wc-shipstation/v1/orders

#### Test Case 1.1.1: Basic Order Retrieval
**Objective**: Verify basic order retrieval functionality
**Steps**:
1. Create 5 test orders with different statuses
2. Send GET request to `/wp-json/wc-shipstation/v1/orders`
3. Verify response contains order data
4. Check response structure matches API documentation

**Expected Result**: 
- HTTP 200 response
- JSON response with `sales_orders` array
- Each order contains required fields: `order_id`, `order_number`, `status`, etc.

#### Test Case 1.1.2: Pagination Testing
**Objective**: Test pagination parameters
**Steps**:
1. Create 25 test orders
2. Test `per_page=10&page=1`
3. Test `per_page=10&page=2`
4. Test `per_page=500` (max limit)
5. Test `per_page=1000` (should be capped at 500)

**Expected Result**:
- Correct number of orders returned per page
- Pagination works correctly
- Max limit enforced

#### Test Case 1.1.3: Date Filtering
**Objective**: Test modified_after parameter
**Steps**:
1. Create orders with different modification dates
2. Test with `modified_after=2024-01-01T00:00:00Z`
3. Test with invalid date format
4. Test with future date

**Expected Result**:
- Only orders modified after specified date returned
- Invalid dates handled gracefully
- Future dates return empty results

#### Test Case 1.1.4: Status Mapping
**Objective**: Test status_mapping parameter
**Steps**:
1. Configure different WooCommerce order statuses
2. Test with `status_mapping=["processing:AwaitingShipment"]`
3. Test with multiple status mappings
4. Test with invalid status mappings

**Expected Result**:
- Only orders with mapped statuses returned
- Invalid mappings handled gracefully
- Default export statuses used when no mapping provided

### 1.2 Shipments Endpoint - POST /wp-json/wc-shipstation/v1/orders/shipments

#### Test Case 1.2.1: Shipment Update
**Objective**: Test order shipment updates
**Steps**:
1. Create test order
2. Send POST request with shipment data
3. Verify order status updated
4. Check order notes added
5. Verify fulfillment data saved

**Expected Result**:
- Order status updated correctly
- Shipment tracking information saved
- Order notes reflect shipment update

## 2. Diagnostics Endpoint Testing

### 2.1 GET /wp-json/wc-shipstation/v1/diagnostics/details

#### Test Case 2.1.1: Site Details Retrieval
**Objective**: Verify diagnostic information retrieval
**Steps**:
1. Send GET request to diagnostics endpoint
2. Verify response contains site information
3. Check WordPress/WooCommerce version info
4. Verify plugin information included

**Expected Result**:
- HTTP 200 response
- Complete site diagnostic information
- Accurate version information

## 3. Authentication & Permissions Testing

### Test Case 3.1: Permission Validation
**Objective**: Test API permission requirements
**Steps**:
1. Test with unauthenticated user
2. Test with user without WooCommerce permissions
3. Test with admin user
4. Test with custom capability user

**Expected Result**:
- Unauthenticated requests return 401
- Insufficient permissions return 403
- Authorized users can access endpoints

## 4. Integration Testing

### Test Case 4.1: HPOS Compatibility
**Objective**: Verify High-Performance Order Storage compatibility
**Steps**:
1. Enable HPOS in WooCommerce
2. Create orders using HPOS
3. Test all API endpoints
4. Verify data consistency

**Expected Result**:
- All endpoints work with HPOS enabled
- Order data retrieved correctly
- No performance degradation

### Test Case 4.2: Large Dataset Testing
**Objective**: Test performance with large order volumes
**Steps**:
1. Create 1000+ test orders
2. Test API endpoints with large datasets
3. Monitor response times
4. Check memory usage

**Expected Result**:
- Acceptable response times (<5 seconds)
- No memory exhaustion
- Pagination works efficiently

## 5. Error Handling Testing

### Test Case 5.1: Invalid Parameters
**Objective**: Test error handling for invalid inputs
**Steps**:
1. Send requests with invalid parameters
2. Test malformed JSON in POST requests
3. Test SQL injection attempts
4. Test XSS attempts

**Expected Result**:
- Appropriate error messages returned
- HTTP status codes correct
- Security vulnerabilities prevented

## 6. Backward Compatibility Testing

### Test Case 6.1: Class Rename Impact
**Objective**: Verify renamed classes don't break existing functionality
**Steps**:
1. Test existing ShipStation functionality
2. Verify admin tools still work
3. Check order meta box display
4. Test notification handling

**Expected Result**:
- All existing functionality preserved
- No fatal errors from class renames
- Admin interface works correctly

## 7. Performance Testing

### Test Case 7.1: Concurrent Requests
**Objective**: Test API under concurrent load
**Steps**:
1. Send 10 concurrent API requests
2. Monitor response times
3. Check for race conditions
4. Verify data consistency

**Expected Result**:
- All requests handled successfully
- No data corruption
- Reasonable response times maintained

## 8. Documentation Verification

### Test Case 8.1: API Documentation Accuracy
**Objective**: Verify API documentation matches implementation
**Steps**:
1. Test all documented endpoints
2. Verify parameter descriptions
3. Check response format examples
4. Test authentication requirements

**Expected Result**:
- Documentation accurately reflects API behavior
- All examples work as documented
- No discrepancies between docs and implementation

## Test Data Requirements

### Orders
- Orders with different statuses (pending, processing, completed, etc.)
- Orders with various product types
- Orders with different shipping addresses
- Orders with tax calculations
- Orders with discounts/coupons

### Products
- Simple products
- Variable products
- Products with dimensions/weight
- Products with SKUs
- Digital products

## Success Criteria
- All API endpoints return correct HTTP status codes
- Response data matches documented format
- Performance meets acceptable thresholds
- Security requirements satisfied
- Backward compatibility maintained
- Error handling works correctly

## Risk Assessment
- **High Risk**: Data corruption during shipment updates
- **Medium Risk**: Performance degradation with large datasets
- **Low Risk**: Minor UI inconsistencies in admin

## Test Environment Cleanup
After testing completion:
1. Remove test orders
2. Reset plugin settings
3. Clear any test data
4. Restore original configuration
