<?php
/**
 * Data for the settings page file.
 *
 * @package WC_ShipStation
 */

use WooCommerce\ShipStation\Auth_Controller;

$statuses = wc_get_order_statuses();

// When integration loaded custom statuses is not loaded yet, so we need to
// merge it manually.
if ( function_exists( 'wc_order_status_manager' ) ) {
	$query = new WP_Query(
		array(
			'post_type'        => 'wc_order_status',
			'post_status'      => 'publish',
			'posts_per_page'   => -1,
			'suppress_filters' => 1,
			'orderby'          => 'menu_order',
			'order'            => 'ASC',
		)
	);

	$filtered_statuses = array();
	foreach ( $query->posts as $post_status ) {
		$filtered_statuses[ 'wc-' . $post_status->post_name ] = $post_status->post_title;
	}
	$statuses = array_merge( $statuses, $filtered_statuses );

	wp_reset_postdata();
}

foreach ( $statuses as $key => $value ) {
	$statuses[ $key ] = str_replace( 'wc-', '', $key );
}

$fields = array(
	'auth_key'        => array(
		'title'             => __( 'Authentication Key', 'woocommerce-shipstation-integration' ),
		'description'       => Auth_Controller::get_auth_button_html(),
		'default'           => '',
		'type'              => 'text',
		'desc_tip'          => __( 'This is the <code>Auth Key</code> you set in ShipStation and allows ShipStation to communicate with your store.', 'woocommerce-shipstation-integration' ),
		'custom_attributes' => array(
			'readonly' => 'readonly',
			'hidden'   => 'hidden',
		),
		'value'             => WC_ShipStation_Integration::$auth_key,
	),
	'export_statuses' => array(
		'title'             => __( 'Export Order Statuses&hellip;', 'woocommerce-shipstation-integration' ),
		'type'              => 'multiselect',
		'options'           => $statuses,
		'class'             => 'chosen_select',
		'css'               => 'width: 450px;',
		'description'       => __( 'Define the order statuses you wish to export to ShipStation.', 'woocommerce-shipstation-integration' ),
		'desc_tip'          => true,
		'custom_attributes' => array(
			'data-placeholder' => __( 'Select Order Statuses', 'woocommerce-shipstation-integration' ),
		),
	),
	'shipped_status'  => array(
		'title'       => __( 'Shipped Order Status&hellip;', 'woocommerce-shipstation-integration' ),
		'type'        => 'select',
		'options'     => $statuses,
		'description' => __( 'Define the order status you wish to update to once an order has been shipping via ShipStation. By default this is "Completed".', 'woocommerce-shipstation-integration' ),
		'desc_tip'    => true,
		'default'     => 'wc-completed',
	),
	'gift_enabled'    => array(
		'title'       => __( 'Gift', 'woocommerce-shipstation-integration' ),
		'label'       => __( 'Enable Gift options at checkout page', 'woocommerce-shipstation-integration' ),
		'type'        => 'checkbox',
		'description' => __( 'Allow customer to mark their order as a gift and include a personalized message.', 'woocommerce-shipstation-integration' ),
		'desc_tip'    => __( 'Enable gift fields on the checkout page.', 'woocommerce-shipstation-integration' ),
		'default'     => 'no',
	),
	'logging_enabled' => array(
		'title'       => __( 'Logging', 'woocommerce-shipstation-integration' ),
		'label'       => __( 'Enable Logging', 'woocommerce-shipstation-integration' ),
		'type'        => 'checkbox',
		'description' => __( 'Note: this may log personal information. We recommend using this for debugging purposes only and deleting the logs when finished.', 'woocommerce-shipstation-integration' ),
		'desc_tip'    => __( 'Log all API interactions.', 'woocommerce-shipstation-integration' ),
		'default'     => 'yes',
	),
);

return $fields;
