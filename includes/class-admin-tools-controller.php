<?php
/**
 * Admin Tools controller file.
 *
 * @package WC_ShipStation
 */

namespace WooCommerce\Shipping\ShipStation;

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use WooCommerce\Shipping\ShipStation\API\REST\Orders_Controller;
use WooCommerce\Shipping\ShipStation\Order_Util;
use WC_Order;
use WC_Logger;
use WC_ShipStation_Integration;

/**
 * Admin_Tools_Controller class.
 *
 * Adds admin tools for ShipStation, including migration utilities and query var helpers.
 */
class Admin_Tools_Controller {
	use Order_Util;

	/**
	 * Logger instance.
	 *
	 * @var WC_Logger|null
	 */
	private $log = null;

	/**
	 * Class constructor.
	 */
	public function __construct() {
		add_filter( 'woocommerce_debug_tools', array( $this, 'register_debug_tools' ) );
		add_filter( 'woocommerce_order_query_args', array( $this, 'add_migration_query_vars_for_hpos' ), 10, 1 );
		add_filter( 'woocommerce_order_data_store_cpt_get_orders_query', array( $this, 'add_migration_query_vars_for_cpt' ), 10, 2 );
	}

	/**
	 * Add new tools in Status >> Tools page.
	 *
	 * @param array $tools List of tools.
	 *
	 * @return array
	 */
	public function register_debug_tools( $tools ) {
		if ( ! class_exists( 'Automattic\\WooCommerce\\Internal\\Fulfillments\\Fulfillment' ) ) {
			return $tools;
		}

		$tools['migrate_notification_to_fulfillment'] = array(
			'name'     => __( 'Migrate ShipStation Notification to WooCommerce Fulfillment', 'woocommerce-shipstation-integration' ),
			'button'   => __( 'Migrate', 'woocommerce-shipstation-integration' ),
			'desc'     => __( 'This will migrate all saved ShipStation Notification in order meta into Fulfillment data. Each run will process 1000 orders.', 'woocommerce-shipstation-integration' ),
			'callback' => array( $this, 'migrate_notifications_to_fulfillments' ),
		);

		return $tools;
	}

	/**
	 * Migrate saved ShipStation notifications on orders into WooCommerce Fulfillments.
	 */
	public function migrate_notifications_to_fulfillments() {
		if ( ! class_exists( 'Automattic\\WooCommerce\\Internal\\Fulfillments\\Fulfillment' ) ) {
			return;
		}

		$orders_controller = new Orders_Controller();

		$orders = wc_get_orders(
			array(
				'shipstation_has_notification'           => 1,
				'shipstation_notif_fulfill_not_migrated' => 1,
				'shipstation_has_unbroken_notification'  => 1,
				'limit'                                  => 1000,
				'orderby'                                => 'modified',
				'order'                                  => 'DESC',
				'return'                                 => 'ids',
			)
		);

		if ( empty( $orders ) || ! is_array( $orders ) ) {
			return;
		}

		foreach ( $orders as $order_id ) {
			$order = wc_get_order( $order_id );

			if ( ! $order instanceof WC_Order ) {
				continue;
			}

			$saved_notifications = $order->get_meta( Orders_Controller::$notification_meta_name );
			$notif_migrated      = 0;
			$unbroken_notif_data = true;

			foreach ( $saved_notifications as $notif_id => $saved_notification ) {
				if ( ! is_array( $saved_notification ) ) {
					$unbroken_notif_data = false;
					break 1;
				}

				$is_saved = $orders_controller->maybe_save_to_fulfillments( $order, $saved_notification );

				// Increase the notification migrated value if notification has been saved to fulfillment successfully.
				if ( true === $is_saved ) {
					++$notif_migrated;
				}

				$saved_notification['saved_to_fulfill'] = $is_saved;
				$saved_notifications[ $notif_id ]       = $saved_notification;
			}

			$order->update_meta_data( Orders_Controller::$notification_meta_name, $saved_notifications );

			if ( ! $unbroken_notif_data ) {
				$order->update_meta_data( 'shipstation_notification_broken', 'yes' );
			}

			// Flagged as successfully migrated if notification migrated has the same amount of the notifications.
			if ( count( $saved_notifications ) === $notif_migrated ) {
				$order->update_meta_data( 'shipstation_notif_fulfill_migrated', 'yes' );
			}

			$order->save();
		}
	}

	/**
	 * Handle custom query vars for HPOS when migrating notifications to fulfillments.
	 *
	 * @param array $query_vars Query vars from WC_Order_Query.
	 * @return array Modified $query_vars.
	 */
	public function add_migration_query_vars_for_hpos( $query_vars ) {
		if ( ! self::custom_orders_table_usage_is_enabled() ) {
			return $query_vars;
		}

		if ( ! empty( $query_vars['shipstation_has_unbroken_notification'] ) ) {
			$query_vars['meta_query'][] = array(
				'key'     => 'shipstation_notification_broken',
				'compare' => 'NOT EXISTS',
			);
		}

		if ( ! empty( $query_vars['shipstation_has_notification'] ) ) {
			$query_vars['meta_query'][] = array(
				'key'     => Orders_Controller::$notification_meta_name,
				'compare' => 'EXISTS',
			);
		}

		if ( ! empty( $query_vars['shipstation_notif_fulfill_migrated'] ) ) {
			$query_vars['meta_query'][] = array(
				'key'   => 'shipstation_notif_fulfill_migrated',
				'value' => esc_attr( $query_vars['shipstation_notif_fulfill_migrated'] ),
			);
		}

		if ( ! empty( $query_vars['shipstation_notif_fulfill_not_migrated'] ) ) {
			$query_vars['meta_query'][] = array(
				'key'     => 'shipstation_notif_fulfill_migrated',
				'compare' => 'NOT EXISTS',
			);
		}

		return $query_vars;
	}

	/**
	 * Handle custom query vars for CPT datastore when migrating notifications to fulfillments.
	 *
	 * @param array $query Main query of WC_Order_Query.
	 * @param array $query_vars Query vars from WC_Order_Query.
	 * @return array Modified $query.
	 */
	public function add_migration_query_vars_for_cpt( $query, $query_vars ) {
		if ( ! empty( $query_vars['shipstation_has_unbroken_notification'] ) ) {
			$query['meta_query'][] = array(
				'key'     => 'shipstation_notification_broken',
				'compare' => 'NOT EXISTS',
			);
		}

		if ( ! empty( $query_vars['shipstation_has_notification'] ) ) {
			$query['meta_query'][] = array(
				'key'     => Orders_Controller::$notification_meta_name,
				'compare' => 'EXISTS',
			);
		}

		if ( ! empty( $query_vars['shipstation_notif_fulfill_migrated'] ) ) {
			$query['meta_query'][] = array(
				'key'   => 'shipstation_notif_fulfill_migrated',
				'value' => esc_attr( $query_vars['shipstation_notif_fulfill_migrated'] ),
			);
		}

		if ( ! empty( $query_vars['shipstation_notif_fulfill_not_migrated'] ) ) {
			$query['meta_query'][] = array(
				'key'     => 'shipstation_notif_fulfill_migrated',
				'compare' => 'NOT EXISTS',
			);
		}

		return $query;
	}

	/**
	 * Log something.
	 *
	 * @param string $message Log message.
	 */
	public function log( $message ) {
		if ( ! WC_ShipStation_Integration::$logging_enabled ) {
			return;
		}
		if ( is_null( $this->log ) ) {
			$this->log = new WC_Logger();
		}
		$this->log->add( 'shipstation', $message );
	}
}

new Admin_Tools_Controller();
