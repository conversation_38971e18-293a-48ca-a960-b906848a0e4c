# ShipStation API Documentation

This directory contains documentation for the ShipStation plugin's REST API endpoints.

## Available Documentation

- [Inventory API](inventory-api.md) - Documentation for the inventory management endpoints that allow retrieving and updating product stock levels.
- [Orders API](orders-api.md) - Documentation for the orders management endpoints that allow retrieving and updating order status and fulfillments.

## Purpose

This documentation is intended for developers who need to integrate with the ShipStation plugin's API. It provides detailed information about:

- Available endpoints
- Request parameters
- Response formats
- Authentication requirements
- Example requests and responses

## Authentication

All API endpoints require proper authentication. The API uses WordPress REST API authentication methods, specifically requiring the `manage_woocommerce` capability.

## Getting Started

To get started with the ShipStation API:

1. Review the documentation for the specific API you need to use
2. Ensure you have proper authentication credentials
3. Test your API requests using a tool like Postman or cURL
4. Implement the API calls in your application

## Need Help?

If you encounter any issues or have questions about the API, please refer to the [WooCommerce documentation](https://woocommerce.com/document/shipstation-for-woocommerce/) or contact support.