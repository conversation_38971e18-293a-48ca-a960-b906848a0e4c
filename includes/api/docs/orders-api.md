# ShipStation Orders API Documentation

## Overview

The ShipStation Orders API provides endpoints for retrieving WooCommerce order data and updating order shipment/fulfillment status. This API enables integration with ShipStation to synchronize order status, shipment information, and fulfillment between WooCommerce and ShipStation.

## Authentication

All API requests require authentication. The API uses WordPress REST API authentication methods, specifically requiring the `manage_woocommerce` capability.

## API Endpoints

### Get All Orders

Retrieves order data with pagination and filtering options.

**Endpoint:** `GET /wc-shipstation/v1/orders`

**Parameters:**

| Parameter       | Type    | Required | Default | Description                                      |
|-----------------|---------|----------|---------|--------------------------------------------------|
| page            | integer | No       | 1       | Current page of the order list.                  |
| per_page        | integer | No       | 100     | Maximum number of orders per page (max 500).     |
| modified_after  | string  | No       | —       | Only orders modified after this date/time (ISO8601 or `Y-m-d H:i:s`). |
| status_mapping  | array   | No       | —       | Mapping of WooCommerce order statuses to ShipStation statuses. |

**Response:**

```json
{
  "sales_orders": [
    {
      "order_id": 123,
      "order_number": "1001",
      "status": "completed",
      "paid_date": "2024-06-01T12:00:00.000Z",
      "fulfilled_date": "2024-06-02T09:00:00.000Z",
      "requested_fulfillments": [
        {
          "requested_fulfillment_id": "abc123",
          "ship_to": {
            "name": "Jane Doe",
            "company": "Acme Inc.",
            "phone": "+*********",
            "address_line_1": "123 Main St",
            "address_line_2": "Suite 100",
            "city": "New York",
            "state_province": "NY",
            "postal_code": "10001",
            "country_code": "US"
          },
          "items": [
            {
              "line_item_id": 456,
              "description": "Sample Product",
              "product": {
                "product_id": 789,
                "name": "Sample Product",
                "description": "A WooCommerce product",
                "identifiers": {
                  "sku": "PROD-456",
                  "isbn": "",
                  "fulfillment_sku": ""
                },
                "details": [
                  {
                    "name": "Color",
                    "value": "Red"
                  }
                ],
                "unit_cost": 49.99,
                "weight": {
                  "value": 500,
                  "unit": "Gram"
                },
                "dimensions": {
                  "length": 10,
                  "width": 5,
                  "height": 2,
                  "unit": "Centimeter"
                },
                "urls": {
                  "image_url": "https://example.com/image.jpg",
                  "product_url": "https://example.com/product/789",
                  "thumbnail_url": "https://example.com/thumb.jpg"
                }
              },
              "quantity": 2,
              "unit_price": 49.99,
              "discount_per_unit": 0,
              "discount_per_line": 0,
              "taxes": [
                {
                  "amount": 5.00,
                  "description": "Sales Tax"
                }
              ],
              "item_url": "https://example.com/product/789",
              "modified_date_time": "2024-06-02T09:00:00.000Z"
            }
          ],
          "extensions": {
            "custom_field_1": "SUMMER2024",
            "custom_field_2": "",
            "custom_field_3": ""
          },
          "shipping_preferences": {
            "gift": false
          }
        }
      ],
      "buyer": {
        "buyer_id": "janedoe",
        "name": "Jane Doe",
        "email": "<EMAIL>",
        "phone": "+*********"
      },
      "bill_to": {
        "email": "<EMAIL>",
        "name": "Jane Doe",
        "phone": "+*********",
        "company": "Acme Inc.",
        "address_line_1": "123 Main St",
        "address_line_2": "Suite 100",
        "city": "New York",
        "state_province": "NY",
        "postal_code": "10001",
        "country_code": "US"
      },
      "currency": "USD",
      "payment": {
        "payment_status": "Paid",
        "taxes": [
          {
            "amount": 5.00,
            "description": "Sales Tax"
          }
        ],
        "shipping_charges": [
          {
            "amount": 10.00,
            "description": "Flat Rate"
          }
        ],
        "amount_paid": 109.99,
        "payment_method": "credit_card",
        "coupon_codes": ["SUMMER2024"],
        "adjustments": [
          {
            "description": "Discount from coupon: SUMMER2024",
            "amount": 10.00
          }
        ]
      },
      "ship_from": {
        "name": "Store Owner",
        "company": "My Store",
        "phone": "+1987654321",
        "address_line_1": "456 Commerce St",
        "address_line_2": "",
        "address_line_3": "",
        "city": "Los Angeles",
        "state_province": "CA",
        "postal_code": "90001",
        "country_code": "US"
      },
      "order_url": "https://example.com/order/123",
      "notes": [
        {
          "type": "NotesFromBuyer",
          "text": "Please ship ASAP."
        },
        {
          "type": "GiftMessage",
          "text": "Happy Birthday!"
        },
        {
          "type": "InternalNotes",
          "text": "Order reviewed by admin."
        }
      ],
      "created_date_time": "2024-06-01T12:00:00.000Z",
      "modified_date_time": "2024-06-02T09:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 100,
    "total": 250,
    "total_pages": 3,
    "has_more": true
  }
}
```

**Status Codes:**

- `200 OK`: Request successful.
- `500 Internal Server Error`: Error retrieving orders.

---

### Update Orders Shipments

Updates shipment and fulfillment data for specified orders.

**Endpoint:** `POST /wc-shipstation/v1/orders/shipments`

**Request Body:**

```json
{
  "notifications": [
    {
      "notification_id": "abc123",
      "order_id": 123,
      "order_number": "1001",
      "tracking_number": "TRACK123",
      "tracking_url": "https://carrier.com/track/TRACK123",
      "carrier_code": "UPS",
      "ship_date": "2024-06-03T10:00:00Z",
      "items": [
        {
          "line_item_id": 456,
          "description": "Sample Product",
          "quantity": 2,
          "sku": "PROD-456",
          "product_id": 789
        }
      ],
      "ship_to": {
        "name": "Jane Doe",
        "company": "Acme Inc.",
        "phone": "+*********",
        "address_line_1": "123 Main St",
        "address_line_2": "Suite 100",
        "address_line_3": "",
        "city": "New York",
        "state_province": "NY",
        "postal_code": "10001",
        "country_code": "US",
        "is_verified": false,
        "residential_indicator": "",
        "pickup_location": {
          "carrier_id": "",
          "relay_id": ""
        }
      },
      "ship_from": { ... },         // Same structure as ship_to
      "return_address": { ... },    // Same structure as ship_to
      "fulfillment_cost": 5.00,
      "insurance_cost": 2.00,
      "notify_buyer": true,
      "notes": [
        {
          "type": "InternalNotes",
          "text": "Left at front desk."
        }
      ]
    }
  ]
}
```

**Response:**

```json
{
  "notification_results": [
    {
      "notification_id": "abc123",
      "status": "success",
      "order_id": 123
    },
    {
      "notification_id": "abc124",
      "status": "failure",
      "failure_reason": "Order not found"
    }
  ]
}
```

**Possible Messages:**

- `"success"`: Order shipment updated successfully.
- `"failure"`: Order shipment update failed (see `failure_reason`).

**Status Codes:**

- `200 OK`: Request processed (may include errors for individual items).
- `400 Bad Request`: Invalid request format.

---

## Error Handling

When errors occur with specific notifications, the API will continue processing other items and return information about the errors:

```json
{
  "notification_results": [
    {
      "notification_id": "abc124",
      "status": "failure",
      "failure_reason": "Order not found"
    }
  ]
}
```

---

## Examples

### Example: Get All Orders (First Page)

**Request:**
```
GET /wc-shipstation/v1/orders
```

### Example: Get All Orders (Second Page with 50 Orders Per Page)

**Request:**
```
GET /wc-shipstation/v1/orders?page=2&per_page=50
```

### Example: Update Order Shipment

**Request:**
```
POST /wc-shipstation/v1/orders/shipments
```

**Request Body:**
```json
{
  "notifications": [
    {
      "notification_id": "abc123",
      "order_id": 123,
      "tracking_number": "TRACK123",
      "carrier_code": "UPS",
      "ship_date": "2024-06-03T10:00:00Z",
      "items": [
        {
          "line_item_id": 456,
          "description": "Sample Product",
          "quantity": 2,
          "sku": "PROD-456",
          "product_id": 789
        }
      ],
      "notify_buyer": true
    }
  ]
}
```

---

## Notes

- When updating orders, the API will automatically update order status and fulfillment data as appropriate.
- Pagination is supported for order retrieval.