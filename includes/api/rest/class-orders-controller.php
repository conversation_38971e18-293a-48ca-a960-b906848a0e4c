<?php
/**
 * ShipStation REST API Orders Controller file.
 *
 * @package WC_ShipStation
 */

namespace WooCommerce\Shipping\ShipStation\API\REST;

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use WC_Order;
use WC_ShipStation_Integration;
use WP_REST_Request;
use WP_REST_Response;
use WP_REST_Server;
use WooCommerce\Shipping\ShipStation\Order_Util;
use WooCommerce\Shipping\ShipStation\Checkout;
use Automattic\WooCommerce\Internal\Fulfillments\Fulfillment;
use Automattic\WooCommerce\Internal\DataStores\Fulfillments\FulfillmentsDataStore;
use Automattic\WooCommerce\Internal\Fulfillments\FulfillmentUtils;
use Automattic\WooCommerce\Utilities\NumberUtil;
use Automattic\WooCommerce\Enums\OrderStatus;
use Automattic\WooCommerce\Enums\OrderInternalStatus;

/**
 * Orders_Controller class.
 */
class Orders_Controller extends API_Controller {
	use Order_Util;

	/**
	 * Namespace for the REST API
	 *
	 * @var string
	 */
	protected string $namespace = 'wc-shipstation/v1';

	/**
	 * REST base for the controller.
	 *
	 * @var string
	 */
	protected string $rest_base = 'orders';

	/**
	 * Meta key for storing ShipStation notifications.
	 *
	 * @var string
	 */
	public static string $notification_meta_name = 'shipstation_notification';

	/**
	 * Register the routes for the controller.
	 */
	public function register_routes(): void {
		// Register the endpoint for retrieving order data.
		register_rest_route(
			$this->namespace,
			'/' . $this->rest_base,
			array(
				'methods'             => WP_REST_Server::READABLE,
				'callback'            => array( $this, 'get_orders' ),
				'permission_callback' => array( $this, 'check_get_permission' ),
				'args'                => array(
					'modified_after' => array(
						'description'       => __( 'Cutback time for the modified time of the order to be retrieved.', 'woocommerce-shipstation-integration' ),
						'type'              => 'string',
						'validate_callback' => function ( $param ) {
							if ( empty( $param ) ) {
								return true;
							}

							return false !== strtotime( $param );
						},
					),
					'page'           => array(
						'description'       => __( 'Current page of the order list.', 'woocommerce-shipstation-integration' ),
						'type'              => 'integer',
						'default'           => 1,
						'sanitize_callback' => function ( $value ) {
							return max( 1, absint( $value ) );
						},
						'validate_callback' => function ( $param ) {
							return is_numeric( $param );
						},
					),
					'per_page'       => array(
						'description'       => __( 'Maximum number of items to be returned in result set.', 'woocommerce-shipstation-integration' ),
						'type'              => 'integer',
						'default'           => 100,
						'sanitize_callback' => function ( $value ) {
							return min( max( - 1, absint( $value ) ), 500 ); // Limit between 1 and 500.
						},
						'validate_callback' => function ( $param ) {
							return is_numeric( $param );
						},
					),
					'status_mapping' => array(
						'description'       => __( 'Mapping of WooCommerce order statuses to ShipStation statuses.', 'woocommerce-shipstation-integration' ),
						'type'              => 'array',
						'sanitize_callback' => function ( $value ) {
							if ( empty( $value ) ) {
								return array();
							}

							return is_array( $value ) ? $value : array( $value );
						},
					),
				),
			)
		);

		// Register the endpoint for updating order shipment data.
		register_rest_route(
			$this->namespace,
			'/' . $this->rest_base . '/shipments',
			array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array( $this, 'update_orders_shipments' ),
				'permission_callback' => array( $this, 'check_update_permission' ),
			)
		);
	}

	/**
	 * REST API permission callback.
	 *
	 * @return boolean
	 */
	public function check_get_permission(): bool {
		/**
		 * Filters whether the current user has permissions to manage WooCommerce.
		 *
		 * @since 1.0.0
		 *
		 * @param bool $can_manage_wc Whether the user can manage WooCommerce.
		 */
		return apply_filters( 'wc_shipstation_user_can_manage_wc', wc_rest_check_manager_permissions( 'attributes', 'read' ) );
	}

	/**
	 * REST API permission callback.
	 *
	 * @return boolean
	 */
	public function check_update_permission(): bool {
		/**
		 * Filters whether the current user has permissions to manage WooCommerce.
		 *
		 * @since 1.0.0
		 *
		 * @param bool $can_manage_wc Whether the user can manage WooCommerce.
		 */
		return apply_filters( 'wc_shipstation_user_can_manage_wc', wc_rest_check_manager_permissions( 'attributes', 'create' ) );
	}

	/**
	 * Get mapped WC order status with ShipStation status.
	 *
	 * @return array
	 */
	public function get_order_status_mapping(): array {
		$status_prefix               = 'wc-';
		$wc_awaiting_payment_status  = WC_ShipStation_Integration::$status_mapping[ WC_ShipStation_Integration::AWAITING_PAYMENT_STATUS ];
		$wc_awaiting_shipment_status = WC_ShipStation_Integration::$status_mapping[ WC_ShipStation_Integration::AWAITING_SHIPMENT_STATUS ];
		$wc_on_hold_status           = WC_ShipStation_Integration::$status_mapping[ WC_ShipStation_Integration::ON_HOLD_STATUS ];
		$wc_completed_status         = WC_ShipStation_Integration::$status_mapping[ WC_ShipStation_Integration::COMPLETED_STATUS ];
		$wc_cancelled_status         = WC_ShipStation_Integration::$status_mapping[ WC_ShipStation_Integration::CANCELLED_STATUS ];

		return array(
			str_replace( $status_prefix, '', $wc_awaiting_payment_status )  => WC_ShipStation_Integration::AWAITING_PAYMENT_STATUS,
			str_replace( $status_prefix, '', $wc_awaiting_shipment_status ) => WC_ShipStation_Integration::AWAITING_SHIPMENT_STATUS,
			str_replace( $status_prefix, '', $wc_on_hold_status )           => WC_ShipStation_Integration::ON_HOLD_STATUS,
			str_replace( $status_prefix, '', $wc_completed_status )         => WC_ShipStation_Integration::COMPLETED_STATUS,
			str_replace( $status_prefix, '', $wc_cancelled_status )         => WC_ShipStation_Integration::CANCELLED_STATUS,
		);
	}

	/**
	 * Get mapped WC order status with ShipStation status.
	 *
	 * @return array
	 */
	public function get_order_status_mapping_combined(): array {
		$order_statuses = $this->get_order_status_mapping();

		$combined_statuses = array();

		foreach ( $order_statuses as $wc_status => $shipstation_status ) {
			$combined_statuses[] = $wc_status . ':' . $shipstation_status;
		}

		return $combined_statuses;
	}

	/**
	 * Get the order status mapping.
	 *
	 * @param string $order_status The order status to map.
	 *
	 * @return string
	 */
	public function get_shipstation_status_from_order( string $order_status ): string {
		$status_mapping = $this->get_order_status_mapping();

		if ( isset( $status_mapping[ $order_status ] ) ) {
			return $status_mapping[ $order_status ];
		}

		return 'Unknown';
	}

	/**
	 * Get the order status mapping.
	 *
	 * @param string $status The ShipStation status to map.
	 *
	 * @return string
	 */
	public function get_order_status_from_shipstation( string $status ): string {
		$status_mapping = $this->get_order_status_mapping();

		foreach ( $status_mapping as $wc_status => $shipstation_status ) {
			if ( $status === $shipstation_status ) {
				return $wc_status;
			}
		}

		return false;
	}

	/**
	 * Retrieve the orders data.
	 *
	 * @param WP_REST_Request $request Request object.
	 *
	 * @return WP_REST_Response
	 */
	public function get_orders( WP_REST_Request $request ): WP_REST_Response {
		$request_params = $request->get_params();

		/**
		* Action hook before the `/orders` REST API endpoint process the request parameter.
		*
		* @param array $request_params
		*
		* @since 4.8.0
		*/
		do_action( 'woocommerce_shipstation_get_orders_before_process_request', $request_params );

		// Get parameters.
		$modified_after = isset( $request_params['modified_after'] ) ? strtotime( $request_params['modified_after'] ) : gmdate( 'c', strtotime( 'now' ) );
		$page           = absint( $request_params['page'] ); // Default to page 1.
		$per_page       = intval( $request_params['per_page'] ); // Default to 100 items per page.
		$status_mapping = isset( $request_params['status_mapping'] ) ? $request_params['status_mapping'] : array();

		$status_mapping = is_array( $status_mapping ) ? $status_mapping : array( $status_mapping );
		$order_statuses = array_map(
			function ( $status ) {
				$parts     = explode( ':', $status );
				$wc_status = $this->get_order_status_from_shipstation( $parts[1] );
				return ! empty( $wc_status ) ? 'wc-' . strtolower( $wc_status ) : '';
			},
			$status_mapping
		);

		// Filter out empty statuses.
		$order_statuses = array_filter(
			$order_statuses,
			function ( $status ) {
				return ! empty( $status );
			}
		);

		if ( empty( $order_statuses ) ) {
			// If no statuses are provided, use the default export statuses.
			// This is to ensure that we always have some statuses to query.
			// Default export statuses can be defined in the integration class.
			$order_statuses = WC_ShipStation_Integration::$export_statuses;
			$this->log( __( 'No order statuses provided in the request. Using default export statuses from the settings.', 'woocommerce-shipstation-integration' ) );
		} else {
			// Only use the order status that has been set from the ShipStation plugin settings.
			$order_statuses = array_intersect(
				$order_statuses,
				WC_ShipStation_Integration::$export_statuses
			);

			// If no valid order statuses,
			// use unregistered/invalid order statuses to make sure the WC Order query return 0 order.
			if ( empty( $order_statuses ) ) {
				$order_statuses = array( 'wc-shipstation-unknown' );
			}
		}

		$args = array(
			'status'        => $order_statuses,
			'limit'         => $per_page,
			'paged'         => $page,
			'date_modified' => '>=' . $modified_after,
			'paginate'      => true,
			'return'        => 'ids',
			'orderby'       => 'date_modified',
			'order'         => 'DESC',
		);

		$results = wc_get_orders( $args );

		if ( is_wp_error( $results ) ) {
			return new WP_REST_Response( array( 'message' => __( 'Error retrieving orders.', 'woocommerce-shipstation-integration' ) ), 500 );
		}

		$total_orders = $results->total;

		// Calculate pagination information.
		$total_pages = $results->max_num_pages;
		$has_more    = $page < $total_pages;

		// Prepare the response data.
		$sales_orders_data = array(
			'sales_orders' => array(),
			'pagination'   => array(
				'page'        => $page,
				'per_page'    => $per_page,
				'total'       => $total_orders,
				'total_pages' => $total_pages,
				'has_more'    => $has_more,
			),
		);

		if ( empty( $results->orders ) || empty( $total_orders ) ) {
			// No sales orders found, return an empty response.
			return new WP_REST_Response( $sales_orders_data, 200 );
		}

		foreach ( $results->orders as $order_id ) {
			/**
			 * Allow third party to skip the export of certain order ID.
			 *
			 * @param boolean $flag Flag to skip the export.
			 * @param int     $order_id Order ID.
			 *
			 * @since 4.1.42
			 */
			if ( ! apply_filters( 'woocommerce_shipstation_export_order', true, $order_id ) ) {
				continue;
			}

			/**
			 * Allow third party to change the order object.
			 *
			 * @param \WC_Order $order Order object.
			 *
			 * @since 4.1.42
			 */
			$order = apply_filters( 'woocommerce_shipstation_export_get_order', wc_get_order( $order_id ) );

			if ( ! self::is_wc_order( $order ) ) {
				/* translators: 1: order id */
				$this->log( sprintf( __( 'Order %s can not be found.', 'woocommerce-shipstation-integration' ), $order_id ) );
				continue;
			}

			$sales_orders_data['sales_orders'][] = $this->get_order_data( $order );

			// Add order note to indicate it has been exported to Shipstation.
			if ( 'yes' !== $order->get_meta( '_shipstation_exported', true ) ) {
				$order->add_order_note( __( 'Order has been exported to Shipstation', 'woocommerce-shipstation-integration' ) );
				$order->update_meta_data( '_shipstation_exported', 'yes' );
				$order->save_meta_data();
			}
		}

		return new WP_REST_Response( $sales_orders_data, 200 );
	}

	/**
	 * Get order data for the response.
	 *
	 * @param \WC_Order $order The order object.
	 *
	 * @return array
	 */
	public function get_order_data( $order ): array {
		$extra_args = array();
		/**
		 * Currency code and exchange rate filters.
		 *
		 * These two filters allow 3rd parties to modify the currency code
		 * and exchange rate used for the order before exporting to ShipStation.
		 *
		 * This can be necessary in cases where the order currency doesn't match
		 * the ShipStation account currency. ShipStation does not do currency
		 * conversion, so the conversion must be done before the order is exported.
		 *
		 * @param string    $currency_code The currency code to use for the order.
		 * @param \WC_Order $order WooCommerce Order object.
		 *
		 * @since 4.3.7
		 */
		$currency_code = apply_filters( 'woocommerce_shipstation_export_currency_code', $order->get_currency(), $order );

		/**
		 * Allow 3rd parties to modify the exchange rate used for the order before exporting to ShipStation.
		 *
		 * @param float     $exchange_rate The exchange rate to use for the order.
		 * @param \WC_Order $order Order object.
		 *
		 * @since 4.3.7
		 */
		$extra_args['exchange_rate'] = apply_filters( 'woocommerce_shipstation_export_exchange_rate', 1.00, $order );

		/**
		 * Filter whether order discounts should be exported as a separate line item to ShipStation.
		 *
		 * By default (true), discounts are exported as a separate line item. This has been the
		 * behavior since the beginning and is expected by all existing users and integrations.
		 *
		 * If set to false, the discount amount will instead be applied proportionally across the product line items,
		 * and no separate "Discount" line will be included in the export.
		 *
		 * ⚠️ Changing this behavior may break compatibility with external systems or workflows
		 * that rely on the presence of a separate discount line.
		 *
		 * This filter is provided to give developers flexibility in customizing how discounts
		 * are represented in the ShipStation export.
		 *
		 * @see   https://linear.app/a8c/issue/WOOSHIP-748/discounts-are-added-in-separate-line-item-as-total-discount-instead-of
		 * @see   https://github.com/woocommerce/woocommerce-shipstation/issues/85
		 *
		 * @param bool      $export_discounts_as_separate_item Whether to export discounts as a separate ShipStation line item. Default true.
		 * @param \WC_Order $order                             The WooCommerce order object.
		 *
		 * @return bool Modified flag to control export behavior for discounts.
		 *
		 * @since 4.5.1
		 */
		$extra_args['export_discounts_as_separate_item'] = apply_filters( 'woocommerce_shipstation_export_discounts_as_separate_item', true, $order );

		$paid_date = ! empty( $order->get_date_paid() ) ? $this->get_shipstation_date_format( $order->get_date_paid()->date( 'Y-m-d' ), $order->get_date_paid()->date( 'H:i:s' ) ) : '';

		$formatted_order_number   = ltrim( $order->get_order_number(), '#' );
		$shipstation_order_status = $this->get_shipstation_status_from_order( $order->get_status() );
		$fulfilled_date           = $this->maybe_get_fulfilled_date( $order );

		if ( 'Unknown' === $shipstation_order_status ) {
			// translators: 1: order id, 2: WC order status, 3: shipstation order status.
			$this->log( sprintf( __( 'Order %1$s status is converted from %2$s to %3$s', 'woocommerce-shipstation-integration' ), $order->get_id(), $order->get_status(), $shipstation_order_status ) );
		}

		$order_data = array(
			'order_id'               => $order->get_id(),
			'order_number'           => $formatted_order_number,
			'status'                 => $shipstation_order_status,
			'paid_date'              => $paid_date,
			'fulfilled_date'         => $fulfilled_date,
			'requested_fulfillments' => $this->get_requested_fulfillments( $order, $extra_args ),
			'buyer'                  => $this->get_buyer( $order ),
			'bill_to'                => array(
				'email'          => $order->get_billing_email(),
				'name'           => $order->get_billing_first_name() . ' ' . $order->get_billing_last_name(),
				'phone'          => $order->get_billing_phone(),
				'company'        => $order->get_billing_company(),
				'address_line_1' => $order->get_billing_address_1(),
				'address_line_2' => $order->get_billing_address_2(),
				'city'           => $order->get_billing_city(),
				'state_province' => $order->get_billing_state(),
				'postal_code'    => $order->get_billing_postcode(),
				'country_code'   => $order->get_billing_country(),
			),
			'currency'               => $currency_code,
			'payment'                => $this->get_payment_info( $order, $extra_args ),
			'ship_from'              => array(
				'name'           => '', // TO-DO later.
				'company'        => '', // TO-DO later.
				'phone'          => '', // TO-DO later.
				'address_line_1' => WC()->countries->get_base_address(),
				'address_line_2' => WC()->countries->get_base_address_2(),
				'address_line_3' => '',
				'city'           => WC()->countries->get_base_city(),
				'state_province' => WC()->countries->get_base_state(),
				'postal_code'    => WC()->countries->get_base_postcode(),
				'country_code'   => WC()->countries->get_base_country(),
			),
			'order_url'              => $order->get_checkout_order_received_url(),
			'notes'                  => $this->get_notes( $order ),
			'created_date_time'      => $this->get_shipstation_date_format( $order->get_date_created()->date( 'Y-m-d' ), $order->get_date_created()->date( 'H:i:s' ) ),
			'modified_date_time'     => $this->get_shipstation_date_format( $order->get_date_modified()->date( 'Y-m-d' ), $order->get_date_modified()->date( 'H:i:s' ) ),
		);

		/**
		 * Filter to allow modification of the order data before it is returned.
		 *
		 * This filter is useful for adding more information to the order data such as :
		 * - `tax_identifier`
		 * - `original_order_source`
		 * - `fulfilled_date`
		 *
		 * For more information on all available parameter,
		 * please refer to this ShipStation API documentation : https://ddnmn7gngv.apidog.io/orders-18958392e0.
		 *
		 * @param array $order_data The order data to be returned.
		 *
		 * @since 4.7.6
		 */
		return apply_filters( 'woocommerce_shipstation_orders_controller_get_order_data', $order_data, $order );
	}

	/**
	 * Get fulfilled date.
	 *
	 * @param \WC_Order $order Order object.
	 *
	 * @return string|false
	 */
	public function maybe_get_fulfilled_date( $order ) {
		if (
			! class_exists( 'Automattic\WooCommerce\Internal\DataStores\Fulfillments\FulfillmentsDataStore' )
			|| ! class_exists( 'Automattic\WooCommerce\Internal\Fulfillments\FulfillmentUtils' )
			|| ! $order instanceof WC_Order
		) {
			return '';
		}

		$fulfillment_status = $order->meta_exists( '_fulfillment_status' ) ? $order->get_meta( '_fulfillment_status', true ) : 'no_fulfillments';

		if ( 'fulfilled' !== $fulfillment_status ) {
			return '';
		}

		$data_store      = wc_get_container()->get( FulfillmentsDataStore::class );
		$wc_fulfillments = $data_store->read_fulfillments( WC_Order::class, '' . $order->get_id() );

		if ( empty( $wc_fulfillments ) ) {
			return '';
		}

		$latest_fulfill_date = 0;
		foreach ( $wc_fulfillments as $fulfillment ) {
			$date_updated = strtotime( $fulfillment->get_date_updated() );

			if ( $date_updated > $latest_fulfill_date ) {
				$latest_fulfill_date = $date_updated;
			}
		}

		return 0 === $latest_fulfill_date ? '' : $this->get_shipstation_date_format( gmdate( 'Y-m-d', $latest_fulfill_date ), gmdate( 'H:i:s', $latest_fulfill_date ) );
	}

	/**
	 * Get buyer information from order.
	 *
	 * @param \WC_Order $order Order object.
	 *
	 * @return array
	 */
	public function get_buyer( $order ) {
		$buyer = $order->get_user();

		if ( false !== $buyer ) {
			return array(
				'buyer_id' => $buyer->user_login,
				'name'     => $buyer->user_firstname . ' ' . $buyer->user_lastname,
				'email'    => $buyer->user_email,
				'phone'    => $order->get_billing_phone(),
			);
		}

		return array(
			'name'  => $order->get_billing_first_name() . ' ' . $order->get_billing_last_name(),
			'email' => $order->get_billing_email(),
			'phone' => $order->get_billing_phone(),
		);
	}

	/**
	 * Get Payment info for the order data.
	 *
	 * @param \WC_Order $order Order object.
	 * @param array     $extra_args Extra args.
	 *
	 * @return array
	 */
	public function get_payment_info( $order, $extra_args ) {
		$order_total = $order->get_total() - floatval( $order->get_total_refunded() );

		// Maybe convert the order total.
		if ( 1.00 !== $extra_args['exchange_rate'] ) {
			$order_total = floatval( $order_total * $extra_args['exchange_rate'] );
		}

		$payment_info = array(
			'payment_status'   => $this->get_payment_status( $order->get_status() ),
			'taxes'            => $this->get_taxes( $order->get_taxes(), $extra_args ),
			'shipping_charges' => $this->get_shipping_charges( $order->get_shipping_methods(), $extra_args ),
			'amount_paid'      => $order_total,
			'payment_method'   => $order->get_payment_method(),
		);

		if ( ! empty( $order->get_coupons() ) && is_array( $order->get_coupons() ) ) {
			foreach ( $order->get_coupons() as $coupon ) {
				if ( ! $coupon instanceof \WC_Order_Item_Coupon ) {
					continue;
				}

				$payment_info['coupon_codes'][] = $coupon->get_code();
				$payment_info['adjustments'][]  = array(
					// translators: %1$s is a coupon code.
					'description' => sprintf( __( 'Discount from coupon: %1$s', 'woocommerce-shipstation-integration' ), $coupon->get_code() ),
					'amount'      => $coupon->get_discount(),
				);
			}
		}

		return $payment_info;
	}

	/**
	 * Get the payment status for ShipStation based on WooCommerce order status.
	 *
	 * @param string $order_status The WooCommerce order status.
	 *
	 * @return string
	 */
	public function get_payment_status( $order_status ): string {
		// Map WooCommerce order status to ShipStation payment status.
		switch ( $order_status ) {
			case OrderStatus::PENDING:
				return WC_ShipStation_Integration::AWAITING_PAYMENT_STATUS;
			case OrderStatus::ON_HOLD:
				return WC_ShipStation_Integration::AWAITING_PAYMENT_STATUS;
			case OrderStatus::CANCELLED:
				return WC_ShipStation_Integration::PAYMENT_CANCELLED_STATUS;
			case OrderStatus::REFUNDED:
				return WC_ShipStation_Integration::PAYMENT_CANCELLED_STATUS;
			case OrderStatus::FAILED:
				return WC_ShipStation_Integration::PAYMENT_FAILED_STATUS;
			default:
				return WC_ShipStation_Integration::PAID_STATUS;
		}
	}

	/**
	 * Get the taxes for the order.
	 *
	 * @param array $order_taxes Order taxes.
	 * @param array $extra_args Extra args.
	 *
	 * @return array
	 */
	public function get_taxes( $order_taxes, $extra_args ): array {
		$taxes = array();

		foreach ( $order_taxes as $tax ) {
			if ( ! $tax instanceof \WC_Order_Item_Tax ) {
				continue; // Skip if not a valid order item tax.
			}

			$tax_amount = floatval( $tax->get_tax_total() ) + floatval( $tax->get_shipping_tax_total() );

			// Maybe convert the order total.
			if ( 1.00 !== $extra_args['exchange_rate'] ) {
				$tax_amount = floatval( $tax_amount * $extra_args['exchange_rate'] );
			}

			$taxes[] = array(
				'amount'      => $tax_amount,
				'description' => $tax->get_name(),
			);
		}

		return $taxes;
	}

	/**
	 * Get the shipping charges for the order.
	 *
	 * @param array $order_shipping_methods Order shipping methods.
	 * @param array $extra_args Extra args.
	 *
	 * @return array
	 */
	public function get_shipping_charges( $order_shipping_methods, $extra_args ): array {
		$shipping_charges = array();

		foreach ( $order_shipping_methods as $shipping_method ) {
			if ( ! $shipping_method instanceof \WC_Order_Item_Shipping ) {
				continue; // Skip if not a valid order item shipping.
			}

			$shipping_amount = floatval( $shipping_method->get_total() );

			// Maybe convert the order total.
			if ( 1.00 !== $extra_args['exchange_rate'] ) {
				$shipping_amount = floatval( $shipping_amount * $extra_args['exchange_rate'] );
			}
			$shipping_charges[] = array(
				'amount'      => $shipping_amount,
				'description' => $shipping_method->get_method_title(),
			);
		}

		return $shipping_charges;
	}

	/**
	 * Get the date format expected by ShipStation.
	 *
	 * @param string $date The date in 'Y-m-d' format.
	 * @param string $time The time in 'H:i:s' format.
	 *
	 * @return string
	 */
	public function get_shipstation_date_format( $date, $time ) {
		if ( ! $date || ! $time ) {
			return '';
		}

		// Format the date and time to match ShipStation's expected format.
		return sprintf( '%1$sT%2$s.000Z', $date, $time );
	}

	/**
	 * Get requested fulfillments for the order.
	 *
	 * @param \WC_Order $order The order object.
	 * @param array     $extra_args Extra args.
	 *
	 * @return array
	 */
	public function get_requested_fulfillments( $order, $extra_args ): array {
		$wc_fulfillments = $this->maybe_process_wc_fulfillment( $order, $extra_args );

		if ( ! empty( $wc_fulfillments ) ) {
			// If there are WC fulfillments, use them.
			return $wc_fulfillments;
		}

		$fulfillments      = array();
		$fulfillment_items = array();
		$order_items       = $order->get_items() + $order->get_items( 'fee' );

		foreach ( $order_items as $item_id => $item ) {
			$fulfillment_item = $this->get_fulfillment_item( intval( $item_id ), $order, 0, $extra_args );

			if ( empty( $fulfillment_item ) ) {
				continue;
			}

			$fulfillment_items[] = $fulfillment_item;
		}

		if ( empty( $fulfillment_items ) ) {
			return $fulfillments;
		}

		// Append cart level discount line.
		if ( $extra_args['export_discounts_as_separate_item'] && $order->get_total_discount() ) {
			$order_total_discount = $order->get_total_discount() * -1;

			// Maybe convert order total discount.
			if ( 1.00 !== $extra_args['exchange_rate'] ) {
				$order_total_discount = floatval( $order_total_discount * $extra_args['exchange_rate'] );
			}

			$rounded_total_discount = NumberUtil::round( $order_total_discount, wc_get_price_decimals() );
			$fulfillment_items[]    = array(
				'description' => __( 'Total Discount', 'woocommerce-shipstation-integration' ),
				'quantity'    => 1,
				'unit_price'  => $rounded_total_discount,
			);
		}

		$fulfillments[] = $this->get_fulfillment( $fulfillment_items, $order );

		return $fulfillments;
	}

	/**
	 * Process WooCommerce fulfillments if any.
	 *
	 * @param \WC_Order $order Order object.
	 * @param array     $extra_args Extra arguments.
	 *
	 * @return array.
	 */
	public function maybe_process_wc_fulfillment( $order, $extra_args ) {
		$fulfillments = array();

		if (
			! class_exists( 'Automattic\WooCommerce\Internal\DataStores\Fulfillments\FulfillmentsDataStore' )
			|| ! class_exists( 'Automattic\WooCommerce\Internal\Fulfillments\FulfillmentUtils' )
			|| ! $order instanceof WC_Order
		) {
			return $fulfillments;
		}

		$data_store      = wc_get_container()->get( FulfillmentsDataStore::class );
		$wc_fulfillments = $data_store->read_fulfillments( WC_Order::class, '' . $order->get_id() );
		$pending_items   = FulfillmentUtils::get_pending_items( $order, $wc_fulfillments );

		if ( empty( $wc_fulfillments ) ) {
			return $fulfillments;
		}

		foreach ( $wc_fulfillments as $fulfillment ) {
			$wc_f_id           = $fulfillment->get_id();
			$wc_f_items        = $fulfillment->get_items();
			$fulfillment_items = array();

			foreach ( $wc_f_items as $wc_f_item ) {
				if ( empty( $wc_f_item['item_id'] ) || ! isset( $wc_f_item['qty'] ) ) {
					continue;
				}
				$extra_args['fulfillment_id'] = $wc_f_id;
				$fulfillment_item             = $this->get_fulfillment_item( intval( $wc_f_item['item_id'] ), $order, floatval( $wc_f_item['qty'] ), $extra_args );

				if ( empty( $fulfillment_item ) ) {
					continue;
				}

				$fulfillment_items[] = $fulfillment_item;
			}

			if ( empty( $fulfillment_items ) ) {
				continue;
			}

			// Append cart level discount line.
			if ( $extra_args['export_discounts_as_separate_item'] && $order->get_total_discount() ) {
				$order_total_discount = 0;

				foreach ( $fulfillment_items as $item ) {
					if ( isset( $item['discount_per_line'] ) && is_numeric( $item['discount_per_line'] ) ) {
						$order_total_discount += $item['discount_per_line'];
					}
				}

				$rounded_total_discount = NumberUtil::round( $order_total_discount, wc_get_price_decimals() ) * -1;
				$fulfillment_items[]    = array(
					'description' => __( 'Total Discount', 'woocommerce-shipstation-integration' ),
					'quantity'    => 1,
					'unit_price'  => $rounded_total_discount,
				);
			}

			$fulfillments[] = $this->get_fulfillment( $fulfillment_items, $order, $wc_f_id );
		}

		if ( empty( $pending_items ) || ! is_array( $pending_items ) ) {
			return $fulfillments;
		}

		$fulfillment_items = array();

		foreach ( $pending_items as $item_id => $pending_item ) {
			if ( empty( $pending_item['item_id'] ) || ! isset( $pending_item['qty'] ) ) {
				continue;
			}

			$fulfillment_item = $this->get_fulfillment_item( intval( $pending_item['item_id'] ), $order, floatval( $pending_item['qty'] ), $extra_args );

			if ( empty( $fulfillment_item ) ) {
				continue;
			}

			$fulfillment_items[] = $fulfillment_item;
		}

		// Append cart level discount line.
		if ( $extra_args['export_discounts_as_separate_item'] && $order->get_total_discount() ) {
			$order_total_discount = 0;

			foreach ( $fulfillment_items as $item ) {
				if ( isset( $item['discount_per_line'] ) && is_numeric( $item['discount_per_line'] ) ) {
					$order_total_discount += $item['discount_per_line'];
				}
			}

			$rounded_total_discount = NumberUtil::round( $order_total_discount, wc_get_price_decimals() ) * -1;
			$fulfillment_items[]    = array(
				'description' => __( 'Total Discount', 'woocommerce-shipstation-integration' ),
				'quantity'    => 1,
				'unit_price'  => $rounded_total_discount,
			);
		}

		$fulfillments[] = $this->get_fulfillment( $fulfillment_items, $order );

		return $fulfillments;
	}

	/**
	 * Get fulfillment info.
	 *
	 * @param array     $fulfillment_items Fulfillment items.
	 * @param \WC_Order $order Order object.
	 * @param string    $fulfillment_id WC Fulfillment ID.
	 *
	 * @return array
	 */
	public function get_fulfillment( $fulfillment_items, $order, $fulfillment_id = '' ) {

		$gift         = $this->get_gift( $order );
		$address_data = self::get_address_data( $order );

		return array(
			'requested_fulfillment_id' => $fulfillment_id,
			'ship_to'                  => array(
				'name'           => $address_data['name'],
				'company'        => $address_data['company'],
				'phone'          => $address_data['phone'],
				'address_line_1' => $address_data['address1'],
				'address_line_2' => $address_data['address2'],
				'city'           => $address_data['city'],
				'state_province' => $address_data['state'],
				'postal_code'    => $address_data['postcode'],
				'country_code'   => $address_data['country'],
			),
			'items'                    => $fulfillment_items,
			'extensions'               => $this->get_custom_fields( $order ),
			'shipping_preferences'     => array(
				'gift' => $gift['is_gift'],
			),
		);
	}

	/**
	 * Get fulfillment item from WC Fufillment.
	 *
	 * @param int       $item_id Order item ID.
	 * @param \WC_Order $order Order object.
	 * @param int       $quantity Item quantity.
	 * @param array     $extra_args Extra args.
	 */
	public function get_fulfillment_item( $item_id, $order, $quantity = 0, $extra_args = array() ) {
		$item             = $order->get_item( $item_id );
		$fulfillment_item = array();

		if ( ! $item instanceof \WC_Order_Item ) {
			return $fulfillment_item;
		}

		$product                = is_callable( array( $item, 'get_product' ) ) ? $item->get_product() : false;
		$item_needs_no_shipping = ! $product || ! $product->needs_shipping();
		$item_not_a_fee         = 'fee' !== $item->get_type();

		/**
		 * Allow third party to exclude the item for when an item does not need shipping or is a fee.
		 *
		 * @since 4.1.31
		 */
		if ( apply_filters( 'woocommerce_shipstation_no_shipping_item', $item_needs_no_shipping && $item_not_a_fee, $product, $item ) ) {
			return $fulfillment_item;
		}

		$unit_price        = 0;
		$discount_per_unit = 0;
		$discount_per_line = 0;
		$item_url          = '';
		$item_product      = array();

		if ( 'fee' === $item->get_type() ) {
			$quantity   = 1;
			$unit_price = $order->get_item_total( $item, false, true );

			// Maybe convert fee item total.
			if ( 1.00 !== $extra_args['exchange_rate'] ) {
				$unit_price = floatval( $unit_price * $extra_args['exchange_rate'] );
			}
		}

		// handle product specific data.
		if ( $product && $product->needs_shipping() ) {
			$product_id   = $item->get_variation_id() ? $item->get_variation_id() : $item->get_product_id();
			$item_url     = $product->get_permalink();
			$unit_cost    = is_callable( array( $item, 'get_cogs_value' ) ) ? $item->get_cogs_value() : 0;
			$item_product = array(
				'product_id'  => $product_id,
				'name'        => $item->get_name(),
				'description' => $product->get_description(),
				'identifiers' => array(
					'sku'             => $product->get_sku(),
					'isbn'            => $product->get_global_unique_id(),
					'fulfillment_sku' => isset( $extra_args['fulfillment_id'] ) ? $extra_args['fulfillment_id'] : '',
				),
				'details'     => $this->get_item_details( $item ),
				'unit_cost'   => $unit_cost,
				'weight'      => array(
					'value' => wc_get_weight( floatval( $product->get_weight() ), 'g' ),
					'unit'  => 'Gram', // Assuming kg, adjust as necessary.
				),
				'dimensions'  => array(
					'length' => wc_get_dimension( floatval( $product->get_length() ), 'cm' ),
					'width'  => wc_get_dimension( floatval( $product->get_width() ), 'cm' ),
					'height' => wc_get_dimension( floatval( $product->get_height() ), 'cm' ),
					'unit'   => 'Centimeter', // Assuming cm, adjust as necessary.
				),
				'urls'        => array(
					'image_url'     => $this->get_image_src_url( $product->get_image_id(), 'full' ),
					'product_url'   => $product->get_permalink(),
					'thumbnail_url' => $this->get_image_src_url( $product->get_image_id(), 'woocommerce_thumbnail' ),
				),
			);

			if ( 0 === $quantity ) {
				$quantity = $item->get_quantity() - abs( $order->get_qty_refunded_for_item( $item_id ) );
			}

			$unit_price        = $extra_args['export_discounts_as_separate_item'] ? $order->get_item_subtotal( $item, false, true ) : $order->get_item_total( $item, false, true );
			$discount_per_unit = floatval( $order->get_item_subtotal( $item, false, true ) - floatval( $order->get_item_total( $item, false, true ) ) );
			$discount_per_line = $discount_per_unit * $quantity;

			// Maybe convert item total.
			if ( 1.00 !== $extra_args['exchange_rate'] ) {
				$unit_price        = floatval( $unit_price * $extra_args['exchange_rate'] );
				$discount_per_unit = floatval( $discount_per_unit * $extra_args['exchange_rate'] );
				$discount_per_line = floatval( $discount_per_line * $extra_args['exchange_rate'] );
			}
		}

		if ( 0 === $quantity ) {
			return $fulfillment_item;
		}

		$fulfillment_item = array_filter(
			array(
				'line_item_id'       => $item_id,
				'description'        => $item->get_name(),
				'product'            => $item_product,
				'quantity'           => $quantity,
				'unit_price'         => $unit_price,
				'discount_per_unit'  => $discount_per_unit,
				'discount_per_line'  => $discount_per_line,
				'taxes'              => $this->get_taxes( $item->get_taxes(), $extra_args ),
				'item_url'           => $item_url,
				'modified_date_time' => $this->get_shipstation_date_format( $order->get_date_modified()->date( 'Y-m-d' ), $order->get_date_modified()->date( 'H:i:s' ) ),
			),
			function( $value ) {
				return ! empty( $value );
			}
		);

		return $fulfillment_item;
	}

	/**
	 * Get gift info.
	 *
	 * @param \WC_Order $order Order object.
	 *
	 * @return array
	 */
	public function get_gift( $order ) {
		$gift = array(
			'is_gift'      => false,
			'gift_message' => '',
		);

		// Maybe append the gift and gift message XML element.
		if ( class_exists( 'WooCommerce\Shipping\ShipStation\Checkout' ) && $order->get_meta( Checkout::get_block_prefixed_meta_key( 'is_gift' ) ) ) {
			$gift['is_gift'] = true;

			$gift_message = $order->get_meta( Checkout::get_block_prefixed_meta_key( 'gift_message' ) );

			if ( ! empty( $gift_message ) ) {
				$gift['gift_message'] = wp_specialchars_decode( $gift_message );
			}
		}

		return $gift;
	}

	/**
	 * Get list of notes.
	 *
	 * @param \WC_Order $order Order object.
	 *
	 * @return array
	 */
	public function get_notes( $order ) {

		$notes = array();

		if ( ! empty( $order->get_customer_note() ) ) {
			$notes[] = array(
				'type' => 'NotesFromBuyer',
				'text' => $order->get_customer_note(),
			);
		}

		$gift = $this->get_gift( $order );

		if ( ! empty( $gift['gift_message'] ) ) {
			$notes[] = array(
				'type' => 'GiftMessage',
				'text' => $gift['gift_message'],
			);
		}

		$args = array(
			'post_id' => $order->get_id(),
			'approve' => 'approve',
			'type'    => 'order_note',
		);

		remove_filter( 'comments_clauses', array( 'WC_Comments', 'exclude_order_comments' ), 10 );
		$result_notes = get_comments( $args );
		add_filter( 'comments_clauses', array( 'WC_Comments', 'exclude_order_comments' ), 10, 1 );

		foreach ( $result_notes as $note ) {
			if ( 'WooCommerce' !== $note->comment_author ) {
				$notes[] = array(
					'type' => 'InternalNotes',
					'text' => $note->comment_content,
				);
			}
		}

		return $notes;
	}
	/**
	 * Get custom fields
	 *
	 * @param \WC_Order $order Order object.
	 *
	 * @return array
	 */
	public function get_custom_fields( $order ) {
		$custom_fields = array();

		// Custom fields - 1 is used for coupon codes.
		$custom_fields['custom_field_1'] = implode( ' | ', $order->get_coupon_codes() );

		// Custom fields 2 and 3 can be mapped to a custom field via the following filters.

		/**
		 * Custom fields 2 can be mapped to a custom field via the following filters.
		 *
		 * @since 4.0.1
		 */
		$meta_key = apply_filters( 'woocommerce_shipstation_export_custom_field_2', '' );
		if ( $meta_key ) {
			/**
			 * Allowing third party to modify the custom field 2 value.
			 *
			 * @since 4.1.0
			 */
			$custom_fields['custom_field_2'] = apply_filters( 'woocommerce_shipstation_export_custom_field_2_value', $order->get_meta( $meta_key, true ), $order->get_id() );
		}

		/**
		 * Custom fields 3 can be mapped to a custom field via the following filters.
		 *
		 * @since 4.0.1
		 */
		$meta_key = apply_filters( 'woocommerce_shipstation_export_custom_field_3', '' );
		if ( $meta_key ) {
			/**
			 * Allowing third party to modify the custom field 3 value.
			 *
			 * @since 4.1.0
			 */
			$custom_fields['custom_field_3'] = apply_filters( 'woocommerce_shipstation_export_custom_field_3_value', $order->get_meta( $meta_key, true ), $order->get_id() );
		}

		return $custom_fields;
	}

	/**
	 * Get item details for the order.
	 *
	 * @param \WC_Order_Item $item Order item.
	 *
	 * @return array
	 */
	public function get_item_details( $item ): array {
		$item_details = array();

		add_filter( 'woocommerce_is_attribute_in_product_name', '__return_false' );
		$formatted_meta = $item->get_formatted_meta_data();

		if ( empty( $formatted_meta ) ) {
			return $item_details;
		}

		foreach ( $formatted_meta as $meta_key => $meta ) {
			$item_details[] = array(
				'name'  => $meta->display_key,
				'value' => wp_strip_all_tags( $meta->display_value ),
			);
		}

		return $item_details;
	}

	/**
	 * Get the image source URL for a given image ID and size.
	 *
	 * @param int    $image_id Image ID.
	 * @param string $size     Image size (default is 'full').
	 *
	 * @return string
	 */
	public function get_image_src_url( $image_id, $size = 'full' ): string {
		if ( ! $image_id ) {
			return '';
		}

		$image_src = wp_get_attachment_image_src( $image_id, $size );

		if ( ! is_array( $image_src ) || empty( $image_src[0] ) ) {
			return '';
		}

		return esc_url( $image_src[0] );
	}

	/**
	 * Update orders shipments for specified SKUs (both products and variations).
	 *
	 * @param WP_REST_Request $request Request object.
	 *
	 * @return WP_REST_Response
	 */
	public function update_orders_shipments( WP_REST_Request $request ): WP_REST_Response {
		$request_params = $request->get_json_params();
		$notifications  = isset( $request_params['notifications'] ) && is_array( $request_params['notifications'] ) ? $request_params['notifications'] : array();

		if ( empty( $notifications ) || ! is_array( $notifications ) ) {
			return new WP_REST_Response( 'Invalid request format.', 400 );
		}

		$response       = array();
		$errors         = array();
		$notif_migrated = 0;

		foreach ( $notifications as $notification ) {
			$order_shipped      = false;
			$saved_notification = array(
				'notification_id'  => '',
				'tracking_number'  => '',
				'tracking_url'     => '',
				'carrier_code'     => '',
				'ext_locatin_id'   => '',
				'items'            => array(),
				'ship_to'          => array(),
				'ship_from'        => array(),
				'return_address'   => array(),
				'ship_date'        => '',
				'currency'         => '',
				'fulfillment_cost' => 0.0,
				'insurance_cost'   => 0.0,
				'notify_buyer'     => false,
				'notes'            => array(),
			);

			if ( empty( $notification['notification_id'] ) ) {
				$this->log( __( 'Notification ID is empty for this notification: ', 'woocommerce-shipstation-integration' ) . print_r( $notification, true ) );// phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_print_r --- Its needed for logging
				continue; // Skip if notification ID is not set.
			}

			if ( empty( $notification['order_id'] ) || empty( $notification['items'] ) || ! is_array( $notification['items'] ) ) {
				$response[] = array(
					'notification_id' => $notification['notification_id'],
					'status'          => 'failure',
					'failure_reason'  => __( 'Empty order ID or items', 'woocommerce-shipstation-integration' ),
				);

				// translators: %1$s is the notification id.
				$this->log( sprintf( __( 'Notification ID: %1$s doesnt have order ID or items.', 'woocommerce-shipstation-integration' ), $notification['notification_id'] ) );

				continue; // Skip invalid items.
			}

			if ( ! is_numeric( $notification['order_id'] ) ) {
				$response[] = array(
					'notification_id' => $notification['notification_id'],
					'status'          => 'failure',
					'failure_reason'  => __( 'Order ID is not numeric', 'woocommerce-shipstation-integration' ),
				);

				// translators: %1$s is the order id, %2$d is the notification id.
				$this->log( sprintf( __( 'Order ID: %1$d from notification ID: %2$s is not numeric.', 'woocommerce-shipstation-integration' ), $notification['order_id'], $notification['notification_id'] ) );

				continue; // Skip if product_id is not numeric.
			}

			$order = wc_get_order( absint( $notification['order_id'] ) );

			// Second try using order number if order ID is not found.
			if ( false === $order || ! $order instanceof WC_Order ) {
				$order_number = isset( $notification['order_number'] ) ? $notification['order_number'] : '';
				$order_id     = self::get_order_id_from_order_number( $order_number );
				$order        = wc_get_order( $order_id );
			}

			if ( false === $order || ! $order instanceof WC_Order ) {
				$response[] = array(
					'notification_id' => $notification['notification_id'],
					'status'          => 'failure',
					'failure_reason'  => __( 'Order not found', 'woocommerce-shipstation-integration' ),
				);

				// translators: %1$s is the order number, %2$d is the order ID.
				$this->log( sprintf( __( 'Order number: %1$s or Order ID: %2$d can not be found.', 'woocommerce-shipstation-integration' ), $order_number, absint( $notification['order_id'] ) ) );

				continue; // Skip if order does not exist.
			}

			$order_notification_meta = $order->get_meta( self::$notification_meta_name, true );
			$order_notification_meta = is_array( $order_notification_meta ) ? $order_notification_meta : array();

			$saved_notification = wp_parse_args( $notification, $saved_notification );

			$saved_items = array();

			foreach ( $notification['items'] as $item ) {
				if ( empty( $item['description'] ) && empty( $item['quantity'] ) ) {
					$this->log( __( 'Skipped this item because doesnt have description and quantity: ', 'woocommerce-shipstation-integration' ) . print_r( $item, true ) ); // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_print_r --- Its needed for logging
					continue; // Skip if required fields are not set.
				}

				$saved_item = array(
					'description'  => '',
					'quantity'     => '',
					'line_item_id' => '',
					'sku'          => '',
					'product_id'   => '',
				);

				$saved_item             = wp_parse_args( $item, $saved_item );
				$saved_item['quantity'] = absint( $saved_item['quantity'] );
				$order_item             = $order->get_item( $saved_item['line_item_id'] );

				if ( $order_item instanceof \WC_Order_Item_Product ) {
					$order_item_product        = $order_item->get_product();
					$saved_item['description'] = $order_item->get_name();
					$saved_item['sku']         = $order_item_product->get_sku();
					$saved_item['product_id']  = $order_item->get_id();
				}

				$saved_items[] = $saved_item;

				$this->log( __( 'ShipNotify Item: ', 'woocommerce-shipstation-integration' ) . print_r( $saved_item, true ) ); // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_print_r --- Its needed for logging
			}

			if ( ! empty( $notification['ship_to'] ) ) {
				$saved_notification['ship_to'] = $this->parse_address_info( $notification['ship_to'] );
			}

			if ( ! empty( $notification['ship_from'] ) ) {
				$saved_notification['ship_from'] = $this->parse_address_info( $notification['ship_from'] );
			}

			if ( ! empty( $notification['return_address'] ) ) {
				$saved_notification['return_address'] = $this->parse_address_info( $notification['return_address'] );
			}

			if ( ! empty( $notification['ship_date'] ) && strtotime( $notification['ship_date'] ) ) {
				$saved_notification['ship_date'] = gmdate( 'Y-m-d H:i:s', strtotime( $notification['ship_date'] ) );
			}

			if ( ! empty( $notification['fulfillment_cost'] ) ) {
				$saved_notification['fulfillment_cost'] = floatval( $notification['fulfillment_cost'] );
			}

			if ( ! empty( $notification['insurance_cost'] ) ) {
				$saved_notification['insurance_cost'] = floatval( $notification['insurance_cost'] );
			}

			if ( ! empty( $notification['notify_buyer'] ) ) {
				$saved_notification['notify_buyer'] = filter_var( $notification['notify_buyer'], FILTER_VALIDATE_BOOLEAN );
			}

			$saved_notification['notes'] = $this->parse_notes( $notification['notes'] );

			if ( ! empty( $saved_items ) ) {
				$saved_notification['items'] = $saved_items;
				$this->process_items( $saved_items, $order, $saved_notification );
			}

			$is_saved = $this->maybe_save_to_fulfillments( $order, $saved_notification );

			// Increase the notification migrated value if notification has been saved to fulfillment successfully.
			if ( true === $is_saved ) {
				++$notif_migrated;
			}

			$saved_notification['saved_to_fulfill']                      = $is_saved;
			$order_notification_meta[ $notification['notification_id'] ] = $saved_notification;

			$order->update_meta_data( self::$notification_meta_name, $order_notification_meta );
			$order->save();

			$response[] = array(
				'notification_id' => $notification->notification_id,
				'status'          => 'success',
				'order_id'        => $order->get_id(),
			);
		}

		// Flagged as successfully migrated if notification migrated has the same amount of the notifications.
		if ( count( $notifications ) === $notif_migrated ) {
			$order->update_meta_data( 'shipstation_notif_fulfill_migrated', 'yes' );
			$order->save();
		}

		return new WP_REST_Response(
			array(
				'notification_results' => $response,
			),
			200
		);
	}

	/**
	 * Save the ShipStation notification to WC fulfillment.
	 *
	 * @param \WC_Order $order Order object.
	 * @param array     $notification Saved notification.
	 *
	 * @return bool
	 */
	public function maybe_save_to_fulfillments( $order, $notification ) {
		if ( ! class_exists( 'Automattic\WooCommerce\Internal\Fulfillments\Fulfillment' ) || ! $order instanceof WC_Order || ! is_array( $notification ) ) {
			return false;
		}

		$order_id          = $order->get_id();
		$notify_customer   = isset( $notification['notify_buyer'] ) ? (bool) $notification['notify_buyer'] : false;
		$tracking_number   = ! empty( $notification['tracking_number'] ) ? wc_clean( wp_unslash( $notification['tracking_number'] ) ) : '';
		$tracking_url      = ! empty( $notification['tracking_url'] ) ? wc_clean( wp_unslash( $notification['tracking_url'] ) ) : '';
		$carrier           = ! empty( $notification['carrier_code'] ) ? wc_clean( wp_unslash( $notification['carrier_code'] ) ) : '';
		$fulfillment_items = array();

		foreach ( $notification['items'] as $notification_item ) {
			$fulfillment_items[] = array(
				'item_id' => intval( $notification_item['line_item_id'] ),
				'qty'     => floatval( $notification_item['quantity'] ),
			);
		}

		$fulfillment_params = array(
			'entity_id'    => '' . $order->get_id(),
			'entity_type'  => 'WC_Order',
			'is_fulfilled' => 1,
			'status'       => 'fulfilled',
			'meta_data'    => array(
				array(
					'id'    => 0,
					'key'   => '_shipping_option',
					'value' => 'manual-entry',
				),
				array(
					'id'    => 0,
					'key'   => '_provider_name',
					'value' => '',
				),
			),
		);

		if ( ! empty( $tracking_number ) ) {
			$fulfillment_params['meta_data'][] = array(
				'id'    => 0,
				'key'   => '_tracking_number',
				'value' => $tracking_number,
			);
		}

		if ( ! empty( $tracking_url ) ) {
			$fulfillment_params['meta_data'][] = array(
				'id'    => 0,
				'key'   => '_tracking_url',
				'value' => $tracking_url,
			);
		}

		if ( ! empty( $carrier ) ) {
			$fulfillment_params['meta_data'][] = array(
				'id'    => 0,
				'key'   => '_shipment_provider',
				'value' => $carrier,
			);
		}

		if ( ! empty( $fulfillment_items ) ) {
			$fulfillment_params['meta_data'][] = array(
				'id'    => 0,
				'key'   => '_items',
				'value' => $fulfillment_items,
			);
		}

		// Create a new fulfillment.
		try {
			$fulfillment = new Fulfillment();
			$fulfillment->set_props( $fulfillment_params );
			$fulfillment->set_items( $fulfillment_items );
			$fulfillment->set_meta_data( $fulfillment_params['meta_data'] );
			$fulfillment->set_entity_type( WC_Order::class );
			$fulfillment->set_entity_id( '' . $order->get_id() );

			$fulfillment->save();

			if ( $fulfillment->get_is_fulfilled() && $notify_customer ) {
				/**
				 * Fires when a fulfillment is created and customer notification is enabled.
				 *
				 * This action hook is triggered after a WooCommerce fulfillment object has been
				 * successfully created from a ShipStation notification and the notify_customer
				 * flag is set to true. It allows third-party plugins to send custom notifications
				 * or perform additional actions when an order is fulfilled.
				 *
				 * @since 4.8.0
				 *
				 * @param int                                                       $order_id    The WooCommerce order ID.
				 * @param \Automattic\WooCommerce\Internal\Fulfillments\Fulfillment $fulfillment The fulfillment object that was created.
				 * @param \WC_Order                                                 $order       The WooCommerce order object.
				 */
				do_action( 'woocommerce_fulfillment_created_notification', $order->get_id(), $fulfillment, $order );
			}

			return true;
		} catch ( \Exception $e ) {
			$this->log( __( 'Unable to save the fulfillment: ', 'woocommerce-shipstation-integration' ) . $e->getMessage() );
			return false;
		}

		return false;
	}

	/**
	 * Process items from the ShipStation notification.
	 *
	 * @param array     $items Items from the notification.
	 * @param \WC_Order $order The order object.
	 * @param array     $notification The notification data.
	 *
	 * @return void
	 */
	public function process_items( $items, $order, $notification ) {
		$shipped_items      = array();
		$shipped_item_count = 0;
		$timestamp          = false !== strtotime( $notification['ship_date'] ) ? strtotime( $notification['ship_date'] ) : wp_date( 'U' );
		$tracking_number    = ! empty( $notification['tracking_number'] ) ? wc_clean( wp_unslash( $notification['tracking_number'] ) ) : '';
		$tracking_url       = ! empty( $notification['tracking_url'] ) ? wc_clean( wp_unslash( $notification['tracking_url'] ) ) : '';
		$carrier            = ! empty( $notification['carrier_code'] ) ? wc_clean( wp_unslash( $notification['carrier_code'] ) ) : '';

		foreach ( $items as $item ) {
			$item_sku    = wc_clean( (string) $item['sku'] );
			$item_name   = wc_clean( (string) $item['description'] );
			$qty_shipped = absint( $item['quantity'] );

			if ( $item_sku ) {
				$item_sku = ' (' . $item_sku . ')';
			}

			$item_id = wc_clean( (int) $item['line_item_id'] );
			if ( ! self::is_shippable_item( $order, $item_id ) ) {
				/* translators: 1: item name */
				$this->log( sprintf( __( 'Item %s is not shippable product. Skipping.', 'woocommerce-shipstation-integration' ), $item_name ) );
				continue;
			}

			$shipped_item_count += $qty_shipped;
			$shipped_items[]     = $item_name . $item_sku . ' x ' . $qty_shipped;
		}

		// Number of items in WC order.
		$total_item_count = self::order_items_to_ship_count( $order );

		// If we have a list of shipped items, we can customise the note + see
		// if the order is not yet complete.
		if ( count( $shipped_items ) > 0 ) {
			$order_note = sprintf(
				/* translators: 1) shipped items 2) carrier's name 3) shipped date, 4) tracking number */
				__( '%1$s shipped via %2$s on %3$s with tracking number %4$s.', 'woocommerce-shipstation-integration' ),
				esc_html( implode( ', ', $shipped_items ) ),
				esc_html( $carrier ),
				date_i18n( get_option( 'date_format' ), $timestamp ),
				$tracking_number
			);

			$current_shipped_items = max( (int) $order->get_meta( '_shipstation_shipped_item_count', true ), 0 );

			if ( ( $current_shipped_items + $shipped_item_count ) >= $total_item_count ) {
				$order_shipped = true;
			}

			$this->log(
				sprintf(
					/* translators: 1) number of shipped items 2) total shipped items 3) order ID */
					__( 'Shipped %1$d out of %2$d items in order %3$s', 'woocommerce-shipstation-integration' ),
					$shipped_item_count,
					$total_item_count,
					$order->get_id()
				)
			);

			$order->update_meta_data( '_shipstation_shipped_item_count', $current_shipped_items + $shipped_item_count );
			$order->save_meta_data();
		} else {
			// If we don't have items from SS and order items in WC.
			$order_shipped = 0 === $total_item_count;

			$order_note = sprintf(
				/* translators: 1) carrier's name 2) shipped date, 3) tracking number */
				__( 'Items shipped via %1$s on %2$s with tracking number %3$s (Shipstation).', 'woocommerce-shipstation-integration' ),
				esc_html( $carrier ),
				date_i18n( get_option( 'date_format' ), $timestamp ),
				$tracking_number
			);

			/* translators: 1: order id */
			$this->log( sprintf( __( 'No items found - shipping entire order %d.', 'woocommerce-shipstation-integration' ), $order->get_id() ) );
		}

		$current_status = 'wc-' . $order->get_status();

		// Tracking information - WC Shipment Tracking extension.
		if ( class_exists( 'WC_Shipment_Tracking' ) ) {
			if ( function_exists( 'wc_st_add_tracking_number' ) ) {
				wc_st_add_tracking_number( $order_id, $tracking_number, strtolower( $carrier ), $timestamp );
			} else {
				$order->update_meta_data( '_tracking_provider', strtolower( $carrier ) );
				$order->update_meta_data( '_tracking_number', $tracking_number );
				$order->update_meta_data( '_date_shipped', $timestamp );
				$order->save_meta_data();
				$this->log( __( 'You\'re using Shipment Tracking < 1.4.0. Please update!', 'woocommerce-shipstation-integration' ) );
			}

			$is_customer_note = false;
		} else {
			$is_customer_note = WC_ShipStation_Integration::$shipped_status !== $current_status;
		}

		$tracking_data = array(
			'tracking_number' => $tracking_number,
			'carrier'         => $carrier,
			'ship_date'       => $timestamp,
			'data'            => $notification,
			'xml'             => '',
		);

		/**
		* Allow to override tracking note.
		*
		* @param string    $order_note
		* @param \WC_Order $order
		* @param array     $tracking_data
		*
		* @since 4.5.0
		*/
		$order_note = apply_filters(
			'woocommerce_shipstation_shipnotify_tracking_note',
			$order_note,
			$order,
			$tracking_data
		);

		$order->add_order_note(
			$order_note,
			/**
			* Allow to override should tracking note be sent to customer.
			*
			* @param bool      $is_customer_note
			* @param string    $order_note
			* @param \WC_Order $order
			* @param array     $tracking_data
			*
			* @since 4.5.0
			*/
			apply_filters(
				'woocommerce_shipstation_shipnotify_send_tracking_note',
				$is_customer_note,
				$order_note,
				$order,
				$tracking_data
			)
		);

		/**
		 * Trigger action for other integrations.
		 *
		 * @param \WC_Order $order Order object.
		 * @param array     $tracking_data Tracking data.
		 *
		 * @since 4.0.1
		 */
		do_action(
			'woocommerce_shipstation_shipnotify',
			$order,
			$tracking_data
		);

		// Update order status.
		if (
			/**
			* Allow to override is order shipped flag.
			*
			* @param bool      $order_shipped
			* @param \WC_Order $order
			* @param array     $tracking_data
			*
			* @since 4.5.0
			*/
			apply_filters(
				'woocommerce_shipstation_shipnotify_order_shipped',
				$order_shipped,
				$order,
				$tracking_data
			)
			&& WC_ShipStation_Integration::$shipped_status !== $current_status
		) {
			$order->update_status( WC_ShipStation_Integration::$shipped_status );

			/* translators: 1) order ID 2) shipment status */
			$this->log( sprintf( __( 'Updated order %1$s to status %2$s', 'woocommerce-shipstation-integration' ), $order_id, WC_ShipStation_Integration::$shipped_status ) );

			/**
			 * Trigger action after the order status is changed for other integrations.
			 *
			 * @param \WC_Order $order Order object.
			 * @param array     $tracking_data Tracking data.
			 *
			 * @since 4.5.2
			 */
			do_action(
				'woocommerce_shipstation_shipnotify_status_updated',
				$order,
				$tracking_data
			);
		}
	}

	/**
	 * Parse notes and return a standardized array.
	 *
	 * @param array $notes Notes data, can be an array of objects.
	 *
	 * @return array
	 */
	public function parse_notes( $notes ): array {
		$parsed_notes = array();

		if ( ! is_array( $notes ) || empty( $notes ) ) {
			return $parsed_notes;
		}

		foreach ( $notes as $note ) {
			if ( ! is_object( $note ) || ! isset( $note['type'], $note['text'] ) ) {
				continue; // Skip if note is not valid.
			}

			$parsed_notes[] = array(
				'type' => $note['type'],
				'text' => $note['text'],
			);
		}

		return $parsed_notes;
	}

	/**
	 * Parse address information and return a standardized array.
	 *
	 * @param mixed $address Address data, can be an object or array.
	 *
	 * @return array
	 */
	public function parse_address_info( $address ): array {
		$address         = json_decode( wp_json_encode( $address ), true );
		$default_address = array(
			'name'                  => '',
			'company'               => '',
			'phone'                 => '',
			'address_line_1'        => '',
			'address_line_2'        => '',
			'address_line_3'        => '',
			'city'                  => '',
			'state_province'        => '',
			'postal_code'           => '',
			'country_code'          => '',
			'is_verified'           => false,
			'residential_indicator' => '',
			'pickup_location'       => array(
				'carrier_id' => '',
				'relay_id'   => '',
			),
		);

		return wp_parse_args( $address, $default_address );
	}
}
