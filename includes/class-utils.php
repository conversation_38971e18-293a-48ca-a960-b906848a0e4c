<?php
/**
 * Class Tools file.
 *
 * @package WC_ShipStation
 */

namespace WooCommerce\Shipping\ShipStation;

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Utils Class
 */
class Utils {
	/**
	 * Get all WooCommerce order statuses.
	 *
	 * @return array
	 */
	public static function get_all_order_statuses() {
		$statuses = wc_get_order_statuses();

		// When integration loaded custom statuses is not loaded yet, so we need to
		// merge it manually.
		if ( function_exists( 'wc_order_status_manager' ) ) {
			$result = get_posts(
				array(
					'post_type'        => 'wc_order_status',
					'post_status'      => 'publish',
					'posts_per_page'   => -1,
					'suppress_filters' => 1,
					'orderby'          => 'menu_order',
					'order'            => 'ASC',
				)
			);

			$filtered_statuses = array();
			foreach ( $result as $post_status ) {
				$filtered_statuses[ 'wc-' . $post_status->post_name ] = $post_status->post_title;
			}
			$statuses = array_merge( $statuses, $filtered_statuses );
		}

		foreach ( $statuses as $key => $value ) {
			$statuses[ $key ] = str_replace( 'wc-', '', $key );
		}

		return $statuses;
	}
}
