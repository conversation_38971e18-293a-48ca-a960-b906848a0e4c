<?php
/**
 * ShipStation Authentication Controller Class.
 *
 * Handles the display and management of authentication credentials.
 *
 * @package WC_ShipStation
 */

namespace WooCommerce\ShipStation;

use WC_ShipStation_Integration;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Auth_Controller Class
 *
 * Manages authentication data display and WooCommerce API key generation.
 */
class Auth_Controller {

	/**
	 * Option name for storing WooCommerce API key ID.
	 *
	 * @var string
	 */
	const API_KEY_ID_OPTION = 'woocommerce_shipstation_api_key_id';

	/**
	 * Constructor
	 */
	public function __construct() {
		if ( ! is_admin() ) {
			return;
		}

		// phpcs:ignore WordPress.WP.Capabilities.Unknown --- It's native capability from WooCommerce.
		if ( ! current_user_can( 'manage_woocommerce' ) ) {
			return;
		}

		// phpcs:ignore WordPress.Security.NonceVerification.Recommended -- No action taken, just checking tab.
		$is_setting_page           = ! empty( $_GET['tab'] ) && 'integration' === $_GET['tab'];
		$hide_notice               = get_option( 'wc_shipstation_hide_activate_notice', '' );
		$settings_notice_dismissed = get_user_meta( get_current_user_id(), 'dismissed_shipstation-setup_notice', true );

		if ( ! $is_setting_page && ( 'yes' === $hide_notice && $settings_notice_dismissed ) ) {
			return;
		}

		add_action( 'wp_ajax_shipstation_get_auth_data', array( $this, 'ajax_get_auth_data' ) );
		add_action( 'wp_ajax_shipstation_generate_new_keys', array( $this, 'ajax_generate_new_keys' ) );
		add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_scripts' ) );
		add_action( 'admin_footer', array( $this, 'render_auth_modal' ) );
	}

	/**
	 * Enqueue scripts and styles for auth display.
	 */
	public function enqueue_scripts() {
        wp_enqueue_style( 'shipstation-auth-display', plugins_url( 'assets/css/auth-display.css', WC_SHIPSTATION_FILE ), array(), WC_SHIPSTATION_VERSION );

        wp_enqueue_script(
			'shipstation-auth-display',
			WC_SHIPSTATION_PLUGIN_URL . 'assets/js/auth-display.js',
			array( 'jquery' ),
			WC_SHIPSTATION_VERSION,
			true
		);

		wp_localize_script(
			'shipstation-auth-display',
			'wc_shipstation_auth_params',
			array(
				'ajax_url'              => admin_url( 'admin-ajax.php' ),
				'nonce'                 => wp_create_nonce( 'shipstation_auth_nonce' ),
				'copy_text'             => esc_html__( 'Copied!', 'woocommerce-shipstation-integration' ),
				'show_text'             => esc_html__( 'Show', 'woocommerce-shipstation-integration' ),
				'hide_text'             => esc_html__( 'Hide', 'woocommerce-shipstation-integration' ),
				'error_text'            => esc_html__( 'Error loading authentication data', 'woocommerce-shipstation-integration' ),
				'confirm_text'          => esc_html__( 'Generating new REST API keys will disable your old keys. Connections using them will stop working until you update ShipStation. Continue?', 'woocommerce-shipstation-integration' ),
			)
		);
	}

	/**
	 * Generate or retrieve WooCommerce REST API credentials.
	 *
	 * @return array API credentials.
	 */
	public function maybe_create_api_credentials(): array {
		$api_key_id = get_option( self::API_KEY_ID_OPTION, false );

		if ( $api_key_id && $this->api_key_exists( $api_key_id ) ) {
			return array();
		}

		return $this->generate_api_credentials();
	}

	/**
	 * Check if API key exists by ID.
	 *
	 * @param int $api_key_id The API key ID.
	 *
	 * @return bool True if API key exists, false otherwise.
	 */
	private function api_key_exists( int $api_key_id ): bool {
		global $wpdb;

		$exists = $wpdb->get_var( $wpdb->prepare( "SELECT key_id FROM {$wpdb->prefix}woocommerce_api_keys WHERE key_id = %d", $api_key_id ) );
		return ! empty( $exists );
	}

	/**
	 * Generate new WooCommerce REST API credentials.
	 *
	 * @return array API credentials.
	 */
	private function generate_api_credentials(): array {
		global $wpdb;

		$consumer_key    = 'ck_' . wc_rand_hash();
		$consumer_secret = 'cs_' . wc_rand_hash();
		$table_name      = $wpdb->prefix . 'woocommerce_api_keys';

		$data = array(
			'user_id'         => get_current_user_id(),
			'description'     => __( 'ShipStation Integration', 'woocommerce-shipstation-integration' ),
			'permissions'     => 'read_write',
			'consumer_key'    => wc_api_hash( $consumer_key ),
			'consumer_secret' => $consumer_secret,
			'truncated_key'   => substr( $consumer_key, -7 ),
		);

		$wpdb->insert(
			$table_name,
			$data,
			array(
				'%d',
				'%s',
				'%s',
				'%s',
				'%s',
				'%s',
			)
		);

		$api_key_id = $wpdb->insert_id;
		update_option( self::API_KEY_ID_OPTION, $api_key_id );

		return array(
			'consumer_key'    => $consumer_key,
			'consumer_secret' => $consumer_secret,
			'api_key_id'      => $api_key_id,
		);
	}

	/**
	 * Get all authentication data for display.
	 *
	 * @return array Authentication data array.
	 */
	public function get_auth_data(): array {
		$api_credentials = $this->maybe_create_api_credentials();

		$data = array(
			'site_url' => home_url(),
			'auth_key' => WC_ShipStation_Integration::$auth_key,
		);

		if ( isset( $api_credentials['consumer_secret'] ) && isset( $api_credentials['consumer_key'] ) ) {
			$data['consumer_key']    = $api_credentials['consumer_key'];
			$data['consumer_secret'] = $api_credentials['consumer_secret'];
		}

		return $data;
	}

	/**
	 * Generate new API keys, invalidating the old ones.
	 *
	 * @return array New API credentials.
	 */
	public function generate_new_keys(): array {
		// Delete old credentials from database.
		$old_api_key_id = get_option( self::API_KEY_ID_OPTION, false );
		if ( $old_api_key_id ) {
			$this->delete_api_credentials_by_id( $old_api_key_id );
		}

		// Generate new credentials.
		return $this->generate_api_credentials();
	}

	/**
	 * Delete API credentials from WooCommerce API keys table by ID.
	 *
	 * @param int $api_key_id The API key ID to delete.
	 */
	private function delete_api_credentials_by_id( int $api_key_id ): void {
		global $wpdb;

		$table_name = $wpdb->prefix . 'woocommerce_api_keys';

		$wpdb->delete(
			$table_name,
			array( 'key_id' => $api_key_id ),
			array( '%d' )
		);
	}

	/**
	 * AJAX handler for getting authentication data.
	 */
	public function ajax_get_auth_data() {
		if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( sanitize_text_field( wp_unslash( $_POST['nonce'] ) ), 'shipstation_auth_nonce' ) ) {
			wp_die( esc_html__( 'Security check failed', 'woocommerce-shipstation-integration' ) );
		}

		// phpcs:ignore WordPress.WP.Capabilities.Unknown --- It's native capability from WooCommerce.
		if ( ! current_user_can( 'manage_woocommerce' ) ) {
			wp_die( esc_html__( 'You do not have sufficient permissions.', 'woocommerce-shipstation-integration' ) );
		}

		$auth_data = $this->get_auth_data();

		wp_send_json_success( $auth_data );
	}

	/**
	 * AJAX handler for generating new API keys.
	 */
	public function ajax_generate_new_keys() {
		if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( sanitize_text_field( wp_unslash( $_POST['nonce'] ) ), 'shipstation_auth_nonce' ) ) {
			wp_die( esc_html__( 'Security check failed', 'woocommerce-shipstation-integration' ) );
		}

		// phpcs:ignore WordPress.WP.Capabilities.Unknown --- It's native capability from WooCommerce.
		if ( ! current_user_can( 'manage_woocommerce' ) ) {
			wp_die( esc_html__( 'You do not have sufficient permissions.', 'woocommerce-shipstation-integration' ) );
		}

		$new_credentials = $this->generate_new_keys();

		$auth_data = array(
			'consumer_key'    => $new_credentials['consumer_key'],
			'consumer_secret' => $new_credentials['consumer_secret'],
			'auth_key'        => WC_ShipStation_Integration::$auth_key,
			'site_url'        => home_url(),
		);

		wp_send_json_success( $auth_data );
	}

	/**
	 * Render the authentication modal HTML.
	 */
	public function render_auth_modal() {
		wc_get_template(
			'auth-modal.php',
			array(),
			'',
			WC_SHIPSTATION_ABSPATH . 'templates/'
		);
	}

	/**
	 * Get the "View Authentication Data" button HTML.
	 *
	 * @return string Button HTML.
	 */
	public static function get_auth_button_html(): string {
		return sprintf(
			'<button type="button" id="shipstation-view-auth" class="button button-primary">%s</button>',
			esc_html__( 'View Authentication Data', 'woocommerce-shipstation-integration' )
		);
	}
}
