{"name": "woocommerce/woocommerce-shipstation", "description": "Ship your WooCommerce orders with confidence, save on top carriers, and automate your processes with ShipStation.", "homepage": "https://woocommerce.com/products/shipstation-integration/", "type": "wordpress-plugin", "license": "GPL-2.0+", "archive": {"exclude": ["!/assets", "node_modules", "bin", "tests", "/vendor", "Gruntfile.js", "README.md", "package.json", "package-lock.json", "composer.json", "composer.lock", "phpunit.xml.dist", "woocommerce-shipstation.zip", ".*", "pnpm-lock.yaml"]}, "require-dev": {"wp-coding-standards/wpcs": "*", "dealerdirect/phpcodesniffer-composer-installer": "*", "woocommerce/qit-cli": "*", "woocommerce/woocommerce-sniffs": "*", "phpstan/phpstan": "^2", "szepeviktor/phpstan-wordpress": "^2", "php-stubs/wp-cli-stubs": "*", "lucasbustamante/stubz": "^0"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "scripts": {"phpstan": ["./vendor/bin/phpstan analyse --configuration=.phpstan/local-config.neon --level=2"], "check-security": ["./vendor/bin/phpcs . --ignore=vendor,.git,.phpstan,assets,node_modules,dist --standard=./.phpcs.security.xml  --report-full --report-summary"], "check-php": ["./vendor/bin/phpcs . --ignore=vendor,.git,.phpstan,assets,node_modules,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-php:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,.phpstan,assets,node_modules,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-all": ["./vendor/bin/phpcs . --ignore=vendor,.git,.phpstan,assets,node_modules,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors -s"], "check-all:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,.phpstan,assets,node_modules,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors"], "qit:security": ["pnpm run build && composer install && ./vendor/bin/qit run:security woocommerce-shipstation-integration --zip=woocommerce-shipstation.zip"], "qit:plugin-check": ["pnpm run build && composer install && ./vendor/bin/qit run:plugin-check woocommerce-shipstation-integration --zip=woocommerce-shipstation.zip"]}}